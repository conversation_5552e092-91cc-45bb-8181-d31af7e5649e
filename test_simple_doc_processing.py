#!/usr/bin/env python3
"""
Test simple .doc file processing using available libraries.
"""

import asyncio
from pathlib import Path
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def test_simple_doc_processing():
    """Test simple .doc file processing."""
    
    # Find the Word document that failed
    uploads_dir = Path("uploads")
    word_files = list(uploads_dir.glob("*<PERSON><PERSON>*.doc"))
    
    if not word_files:
        logger.error("No Hulda Clark Word document found in uploads directory")
        return
    
    word_file = word_files[0]
    logger.info(f"Testing simple .doc processing on: {word_file}")
    
    # Try different approaches
    success = False
    
    # Method 1: Try python-docx (might work for some .doc files)
    try:
        logger.info("Trying python-docx...")
        from docx import Document
        
        doc = Document(str(word_file))
        text_content = []
        
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():
                text_content.append(paragraph.text.strip())
        
        if text_content:
            full_text = '\n'.join(text_content)
            logger.info(f"✅ python-docx succeeded! Extracted {len(full_text)} characters")
            logger.info(f"📝 Preview: {full_text[:200]}...")
            success = True
        else:
            logger.warning("python-docx returned empty content")
            
    except Exception as e:
        logger.warning(f"python-docx failed: {e}")
    
    # Method 2: Try reading as binary and extracting readable text
    if not success:
        try:
            logger.info("Trying binary text extraction...")
            
            with open(word_file, 'rb') as f:
                binary_data = f.read()
            
            # Try to decode and extract readable text
            try:
                # Try different encodings
                for encoding in ['utf-8', 'latin-1', 'cp1252', 'utf-16']:
                    try:
                        text = binary_data.decode(encoding, errors='ignore')
                        # Filter out non-printable characters
                        readable_text = ''.join(char for char in text if char.isprintable() or char.isspace())
                        
                        # Look for substantial text content
                        words = readable_text.split()
                        meaningful_words = [word for word in words if len(word) > 2 and word.isalpha()]
                        
                        if len(meaningful_words) > 50:  # If we found substantial text
                            logger.info(f"✅ Binary extraction with {encoding} succeeded!")
                            logger.info(f"📝 Found {len(meaningful_words)} meaningful words")
                            logger.info(f"📝 Preview: {' '.join(meaningful_words[:20])}...")
                            success = True
                            break
                            
                    except Exception as e:
                        continue
                        
            except Exception as e:
                logger.warning(f"Binary text extraction failed: {e}")
                
        except Exception as e:
            logger.warning(f"Binary reading failed: {e}")
    
    # Method 3: Try using textract if available
    if not success:
        try:
            logger.info("Trying textract...")
            import textract
            
            text = textract.process(str(word_file)).decode('utf-8')
            if text and len(text.strip()) > 100:
                logger.info(f"✅ textract succeeded! Extracted {len(text)} characters")
                logger.info(f"📝 Preview: {text[:200]}...")
                success = True
            else:
                logger.warning("textract returned minimal content")
                
        except ImportError:
            logger.warning("textract not available")
        except Exception as e:
            logger.warning(f"textract failed: {e}")
    
    if not success:
        logger.error("❌ All simple processing methods failed")
        logger.info("💡 Suggestion: Convert the .doc file to .docx or .pdf manually for better processing")
    
    return success

if __name__ == "__main__":
    asyncio.run(test_simple_doc_processing())
