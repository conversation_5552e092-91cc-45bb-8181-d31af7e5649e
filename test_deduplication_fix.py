#!/usr/bin/env python3
"""
Test script to verify entity deduplication fixes.

This script tests the entity deduplication with the new lower thresholds
and improved logging to ensure entities are actually being merged.
"""

import asyncio
import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication import EntityDeduplicator
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = get_logger(__name__)

async def test_deduplication():
    """Test entity deduplication with improved settings."""
    
    logger.info("🧪 Testing entity deduplication with new settings...")
    
    try:
        # Initialize deduplicator
        deduplicator = EntityDeduplicator()
        
        # Test with a small batch first
        logger.info("Running deduplication on all entities...")
        result = await deduplicator.deduplicate_entities(
            merge=True,
            limit=100  # Test with first 100 entities
        )
        
        # Log detailed results
        logger.info("📊 Deduplication Results:")
        logger.info(f"  Total entities processed: {result.total_entities}")
        logger.info(f"  Duplicate groups found: {result.duplicate_groups}")
        logger.info(f"  Total duplicate matches: {result.total_duplicates}")
        logger.info(f"  Entities merged: {result.merged_entities}")
        logger.info(f"  Processing time: {result.processing_time:.2f} seconds")
        
        if result.total_duplicates > 0 and result.merged_entities == 0:
            logger.warning("⚠️  Found duplicates but merged 0 entities - check thresholds!")
            
            # Show some example matches
            if result.entity_matches:
                logger.info("Example duplicate matches found:")
                for i, match in enumerate(result.entity_matches[:5]):
                    logger.info(f"  {i+1}. '{match.source_name}' -> '{match.target_name}' (score: {match.similarity_score:.3f})")
        
        elif result.merged_entities > 0:
            logger.info(f"✅ Successfully merged {result.merged_entities} entities!")
            
        else:
            logger.info("ℹ️  No duplicates found to merge")
            
        return result
        
    except Exception as e:
        logger.error(f"❌ Error during deduplication test: {e}", exc_info=True)
        return None

async def test_embedding_logging():
    """Test embedding generation logging."""
    
    logger.info("🧪 Testing embedding generation logging...")
    
    try:
        from services.embedding_processor import EmbeddingProcessor
        
        # Get a document to test with
        from database.database_service import get_falkordb_adapter
        adapter = await get_falkordb_adapter()
        
        # Find a document with facts
        query = """
        MATCH (e:Episode)-[:CONTAINS]->(f:Fact)
        RETURN e.uuid AS episode_id
        LIMIT 1
        """
        
        result = adapter.execute_cypher(query)
        
        if result and len(result) > 1 and result[1]:
            episode_id = result[1][0][0]
            logger.info(f"Testing embedding generation for episode: {episode_id}")
            
            # Test embedding generation
            embedding_processor = EmbeddingProcessor()
            embedding_result = await embedding_processor.generate_embeddings_for_document(episode_id)
            
            logger.info("📊 Embedding Results:")
            logger.info(f"  Embeddings generated: {embedding_result.get('embeddings_generated', 0)}")
            logger.info(f"  Model used: {embedding_result.get('embedding_model', 'unknown')}")
            
        else:
            logger.info("No documents with facts found for embedding test")
            
    except Exception as e:
        logger.error(f"❌ Error during embedding test: {e}", exc_info=True)

async def main():
    """Main test function."""
    
    logger.info("🚀 Starting deduplication and embedding tests...")
    
    # Test deduplication
    await test_deduplication()
    
    # Test embedding logging
    await test_embedding_logging()
    
    logger.info("✅ Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
