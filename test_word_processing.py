#!/usr/bin/env python3
"""
Test Word document processing with the new fallback methods.
"""

import asyncio
from pathlib import Path
from processors.word_processor import WordProcessor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def test_word_processing():
    """Test Word document processing with fallback methods."""
    
    # Find the Word document that failed
    uploads_dir = Path("uploads")
    word_files = list(uploads_dir.glob("*<PERSON><PERSON>*.doc"))
    
    if not word_files:
        logger.error("No Hulda Clark Word document found in uploads directory")
        return
    
    word_file = word_files[0]
    logger.info(f"Testing Word processing on: {word_file}")
    
    # Initialize the Word processor
    processor = WordProcessor()
    
    # Test the extraction
    try:
        result = await processor.extract_text(word_file)
        
        if result['success']:
            logger.info(f"✅ Successfully extracted text using {result.get('extraction_method', 'unknown')}")
            logger.info(f"📄 Text length: {len(result['text'])} characters")
            logger.info(f"📊 Metadata: {result.get('metadata', {})}")
            
            # Show first 500 characters
            text_preview = result['text'][:500]
            logger.info(f"📝 Text preview:\n{text_preview}...")
            
        else:
            logger.error(f"❌ Failed to extract text: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"❌ Exception during processing: {e}")

if __name__ == "__main__":
    asyncio.run(test_word_processing())
