#!/usr/bin/env python3
"""
Test the Docling processor for .doc files.
"""

import async<PERSON>
from pathlib import Path
from processors.docling_processor import DoclingProcessor
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def test_docling_processor():
    """Test Docling processor with .doc files."""
    
    # Find the Word document that failed
    uploads_dir = Path("uploads")
    word_files = list(uploads_dir.glob("*<PERSON><PERSON> Clark*.doc"))
    
    if not word_files:
        logger.error("No Hulda Clark Word document found in uploads directory")
        return
    
    word_file = word_files[0]
    logger.info(f"Testing Docling processor on: {word_file}")
    
    # Initialize the Docling processor
    processor = DoclingProcessor()
    
    # Check if file is supported
    if not processor.supports_file(str(word_file)):
        logger.error(f"File type not supported by Docling: {word_file.suffix}")
        return
    
    # Test the extraction
    try:
        result = await processor.extract_text(word_file)
        
        if result['success']:
            logger.info(f"✅ Successfully extracted text using <PERSON>ling")
            logger.info(f"📄 Text length: {len(result['text'])} characters")
            logger.info(f"📊 Metadata: {result.get('metadata', {})}")
            
            # Show first 500 characters
            text_preview = result['text'][:500]
            logger.info(f"📝 Text preview:\n{text_preview}...")
            
            # Test preview functionality
            logger.info("\n🔍 Testing preview functionality...")
            preview_result = await processor.preview_document(word_file, max_chars=200)
            if preview_result['success']:
                logger.info(f"📋 Preview: {preview_result['preview_text']}")
            
        else:
            logger.error(f"❌ Failed to extract text: {result.get('error', 'Unknown error')}")
            
    except Exception as e:
        logger.error(f"❌ Exception during processing: {e}")

if __name__ == "__main__":
    asyncio.run(test_docling_processor())
