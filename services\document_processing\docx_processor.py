"""
DOCX document processor for Microsoft Word documents.
"""

from typing import Dict, Any
import logging
from pathlib import Path

from .base_processor import BaseDocumentProcessor

logger = logging.getLogger(__name__)

class DocxProcessor(BaseDocumentProcessor):
    """
    DOCX document processor for Microsoft Word documents.
    """
    
    def __init__(self):
        """Initialize the DOCX processor."""
        super().__init__()
        self.supported_extensions = {'.docx', '.doc'}
    
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports DOCX files."""
        return self.get_file_extension(file_path) in self.supported_extensions
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process a DOCX document and extract text.
        
        Args:
            file_path: Path to the DOCX file
            
        Returns:
            Processing result dictionary
        """
        self.log_processing_start(file_path)
        
        # Validate file
        validation = self.validate_file(file_path)
        if not validation["valid"]:
            return {
                "success": False,
                "error": validation["error"]
            }
        
        try:
            extension = self.get_file_extension(file_path)
            
            if extension == '.docx':
                text, doc_metadata = await self._extract_from_docx(file_path)
            elif extension == '.doc':
                text, doc_metadata = await self._extract_from_doc(file_path)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported file extension: {extension}"
                }
            
            if not text:
                return {
                    "success": False,
                    "error": "No text content found in document"
                }
            
            # Extract metadata
            metadata = await self.extract_metadata(file_path)
            metadata.update(doc_metadata)
            
            self.log_processing_success(file_path, len(text))
            
            return {
                "success": True,
                "text": text,
                "metadata": metadata
            }
            
        except Exception as e:
            error_msg = f"Error processing DOCX file: {str(e)}"
            self.log_processing_error(file_path, error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def _extract_from_docx(self, file_path: str) -> tuple[str, Dict[str, Any]]:
        """Extract text and metadata from DOCX file."""
        try:
            from docx import Document
            
            doc = Document(file_path)
            
            # Extract text from paragraphs
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            # Extract text from tables
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        text_parts.append(" | ".join(row_text))
            
            text = "\n".join(text_parts)
            
            # Extract document metadata
            metadata = {
                "paragraph_count": len(doc.paragraphs),
                "table_count": len(doc.tables),
                "word_count": len(text.split()),
                "character_count": len(text)
            }
            
            # Extract core properties if available
            if hasattr(doc, 'core_properties'):
                core_props = doc.core_properties
                metadata.update({
                    "title": getattr(core_props, 'title', '') or '',
                    "author": getattr(core_props, 'author', '') or '',
                    "subject": getattr(core_props, 'subject', '') or '',
                    "keywords": getattr(core_props, 'keywords', '') or '',
                    "comments": getattr(core_props, 'comments', '') or '',
                    "created": str(getattr(core_props, 'created', '') or ''),
                    "modified": str(getattr(core_props, 'modified', '') or ''),
                    "last_modified_by": getattr(core_props, 'last_modified_by', '') or ''
                })
            
            return text, metadata
            
        except ImportError:
            logger.error("python-docx not installed. Install with: pip install python-docx")
            raise ImportError("python-docx library required for DOCX processing")
        except Exception as e:
            logger.error(f"Error extracting from DOCX: {e}")
            raise
    
    async def _extract_from_doc(self, file_path: str) -> tuple[str, Dict[str, Any]]:
        """Extract text and metadata from DOC file."""
        try:
            # Try using python-docx2txt for .doc files
            import docx2txt
            
            text = docx2txt.process(file_path)
            
            metadata = {
                "word_count": len(text.split()) if text else 0,
                "character_count": len(text) if text else 0,
                "extraction_method": "docx2txt"
            }
            
            return text or "", metadata
            
        except ImportError:
            # Fallback to antiword if available
            return await self._extract_with_antiword(file_path)
        except Exception as e:
            logger.warning(f"docx2txt failed: {e}, trying antiword")
            return await self._extract_with_antiword(file_path)
    
    async def _extract_with_antiword(self, file_path: str) -> tuple[str, Dict[str, Any]]:
        """Extract text using antiword command-line tool."""
        try:
            import subprocess
            
            result = subprocess.run(
                ['antiword', file_path],
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                text = result.stdout
                metadata = {
                    "word_count": len(text.split()) if text else 0,
                    "character_count": len(text) if text else 0,
                    "extraction_method": "antiword"
                }
                return text, metadata
            else:
                raise Exception(f"antiword failed with return code {result.returncode}")
                
        except FileNotFoundError:
            logger.warning("antiword not found, trying binary text extraction")
            return await self._extract_binary_text(file_path)
        except subprocess.TimeoutExpired:
            logger.warning("antiword processing timed out, trying binary text extraction")
            return await self._extract_binary_text(file_path)
        except Exception as e:
            logger.warning(f"antiword extraction failed: {e}, trying binary text extraction")
            return await self._extract_binary_text(file_path)

    async def _extract_binary_text(self, file_path: str) -> tuple[str, Dict[str, Any]]:
        """Extract text from .doc file using binary reading and text filtering."""
        try:
            logger.info(f"Attempting binary text extraction from {file_path}")

            with open(file_path, 'rb') as f:
                binary_data = f.read()

            # Try different encodings to extract readable text
            best_text = ""
            best_encoding = "unknown"

            for encoding in ['utf-8', 'latin-1', 'cp1252', 'utf-16']:
                try:
                    # Decode with error handling
                    decoded_text = binary_data.decode(encoding, errors='ignore')

                    # Filter out non-printable characters but keep spaces and newlines
                    filtered_text = ''.join(
                        char for char in decoded_text
                        if char.isprintable() or char in [' ', '\n', '\t']
                    )

                    # Look for meaningful text content
                    words = filtered_text.split()
                    meaningful_words = [
                        word for word in words
                        if len(word) > 2 and any(c.isalpha() for c in word)
                    ]

                    # If this encoding gives us more meaningful content, use it
                    if len(meaningful_words) > len(best_text.split()):
                        best_text = ' '.join(meaningful_words)
                        best_encoding = encoding

                except Exception:
                    continue

            if best_text and len(best_text.strip()) > 50:
                logger.info(f"✅ Binary extraction succeeded with {best_encoding} encoding")
                logger.info(f"Extracted {len(best_text)} characters")

                metadata = {
                    "word_count": len(best_text.split()),
                    "character_count": len(best_text),
                    "extraction_method": f"binary_text_{best_encoding}",
                    "encoding_used": best_encoding
                }

                return best_text, metadata
            else:
                logger.error("Binary text extraction found insufficient readable content")
                raise Exception("No readable text content found in .doc file")

        except Exception as e:
            logger.error(f"Binary text extraction failed: {e}")
            raise ImportError("All DOC file processing methods failed")
