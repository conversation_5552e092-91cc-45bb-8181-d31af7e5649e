"""
Main entity deduplication logic.
"""

import asyncio
import logging
import time
from typing import Dict, List, Optional, Set, Tuple, Any, Union
import uuid

from entity_deduplication.models import EntityForDeduplication, EntityMatch, DeduplicationResult
from entity_deduplication.utils import (
    calculate_entity_similarity,
    create_entity_match,
    chunk_entities
)
from entity_deduplication.config import (
    BATCH_SIZE,
    FETCH_BATCH_SIZE,
    MAX_ENTITIES_PER_REQUEST,
    USE_LLM_FOR_DEDUPLICATION,
    LLM_PROVIDER,
    LLM_MODEL,
    VERBOSE_LOGGING
)

# Set up logging
logger = logging.getLogger(__name__)
if VERBOSE_LOGGING:
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)


class EntityDeduplicator:
    """
    Class for deduplicating entities in the knowledge graph.

    This class provides methods for finding and merging duplicate entities
    using string similarity, vector embeddings, and optionally LLM-based semantic matching.
    """

    def __init__(self, database_adapter=None, llm_client=None):
        """
        Initialize the entity deduplicator.

        Args:
            database_adapter: Adapter for database operations
            llm_client: Client for LLM operations (optional)
        """
        self.database_adapter = database_adapter
        self.llm_client = llm_client

    async def get_database_adapter(self):
        """
        Get the database adapter, initializing it if necessary.

        Returns:
            Database adapter
        """
        if not self.database_adapter:
            from database.falkordb_adapter import get_falkordb_adapter
            self.database_adapter = await get_falkordb_adapter()
        return self.database_adapter

    async def get_llm_client(self):
        """
        Get the LLM client, initializing it if necessary.

        Returns:
            LLM client
        """
        if not self.llm_client and USE_LLM_FOR_DEDUPLICATION:
            try:
                if LLM_PROVIDER == "openrouter":
                    try:
                        from utils.open_router_client import OpenRouterClient
                        self.llm_client = OpenRouterClient(model=LLM_MODEL)
                    except ImportError:
                        logger.warning("OpenRouterClient not found. LLM-based deduplication will be disabled.")
                elif LLM_PROVIDER == "ollama":
                    try:
                        from utils.ollama_client import OllamaClient
                        self.llm_client = OllamaClient(model=LLM_MODEL)
                    except ImportError:
                        logger.warning("OllamaClient not found. LLM-based deduplication will be disabled.")
                else:
                    logger.warning(f"Unsupported LLM provider: {LLM_PROVIDER}. LLM-based deduplication will be disabled.")
            except Exception as e:
                logger.error(f"Error initializing LLM client: {e}")
        return self.llm_client

    async def fetch_entities(
        self,
        entity_type: Optional[str] = None,
        document_id: Optional[str] = None,
        limit: int = MAX_ENTITIES_PER_REQUEST
    ) -> List[EntityForDeduplication]:
        """
        Fetch entities from the database.

        Args:
            entity_type: Type of entities to fetch (optional)
            document_id: ID of the document to fetch entities from (optional)
            limit: Maximum number of entities to fetch (0 means no limit)

        Returns:
            List of entities
        """
        adapter = await self.get_database_adapter()
        all_entities = []

        # Build base query
        base_query = "MATCH (e:Entity)"
        where_clauses = []
        params = {}

        # Add required field checks
        where_clauses.append("e.uuid IS NOT NULL")
        where_clauses.append("e.name IS NOT NULL")
        where_clauses.append("e.type IS NOT NULL")

        if entity_type:
            where_clauses.append("e.type = $type")
            params["type"] = entity_type

        if document_id:
            where_clauses.append("e.source_document_id = $document_id")
            params["document_id"] = document_id

        if where_clauses:
            base_query += " WHERE " + " AND ".join(where_clauses)

        # First, get the total count of entities
        count_query = base_query.replace("MATCH (e:Entity)", "MATCH (e:Entity)")
        count_query += " RETURN count(e) as count"

        count_result = adapter.execute_cypher(count_query, params)
        total_entities = 0
        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            total_entities = count_result[1][0][0]

        logger.info(f"Total entities to process: {total_entities}")

        # If limit is set and less than total, use the limit
        if limit > 0 and limit < total_entities:
            total_entities = limit
            logger.info(f"Limited to processing {limit} entities")

        # Process in batches
        batch_size = FETCH_BATCH_SIZE
        offset = 0

        while offset < total_entities:
            # Adjust batch size for the last batch
            current_batch_size = min(batch_size, total_entities - offset)

            # Build query with SKIP and LIMIT for pagination
            query = base_query + " RETURN e.uuid as uuid, e.name as name, e.type as type, " \
                    "e.description as description, e.source_document_id as source_document_id, " \
                    "e.source_fact_id as source_fact_id, e.confidence as confidence, " \
                    "e.created_at as created_at SKIP $skip LIMIT $limit"

            batch_params = params.copy()
            batch_params["skip"] = offset
            batch_params["limit"] = current_batch_size

            # Execute query
            logger.info(f"Fetching entities batch {offset+1}-{offset+current_batch_size} of {total_entities}")
            result = adapter.execute_cypher(query, batch_params)

            # Process results
            batch_entities = []
            if result and len(result) > 1:
                for row in result[1]:
                    # Skip entities with missing required fields
                    if row[0] is None or row[1] is None or row[2] is None:
                        logger.warning(f"Skipping entity with missing required fields: {row}")
                        continue

                    try:
                        entity = EntityForDeduplication(
                            uuid=row[0] if row[0] is not None else str(uuid.uuid4()),
                            name=row[1] if row[1] is not None else "Unknown",
                            type=row[2] if row[2] is not None else "Other",
                            description=row[3] if len(row) > 3 and row[3] is not None else None,
                            source_document_id=row[4] if len(row) > 4 and row[4] is not None else None,
                            source_fact_id=row[5] if len(row) > 5 and row[5] is not None else None,
                            confidence=row[6] if len(row) > 6 and row[6] is not None else None,
                            created_at=row[7] if len(row) > 7 and row[7] is not None else None
                        )
                        batch_entities.append(entity)
                    except Exception as e:
                        logger.error(f"Error creating entity from row {row}: {e}")
                        continue

            logger.info(f"Fetched {len(batch_entities)} entities in current batch")
            all_entities.extend(batch_entities)

            # Move to next batch
            offset += current_batch_size

            # If we've reached the limit, stop
            if limit > 0 and len(all_entities) >= limit:
                all_entities = all_entities[:limit]
                break

        logger.info(f"Total entities fetched: {len(all_entities)}")
        return all_entities

    async def find_duplicates(
        self,
        entities: List[EntityForDeduplication]
    ) -> List[EntityMatch]:
        """
        Find duplicate entities using similarity matching.

        Args:
            entities: List of entities to check for duplicates

        Returns:
            List of entity matches
        """
        start_time = time.time()
        matches = []

        # Process in batches to avoid memory issues
        entity_chunks = chunk_entities(entities, BATCH_SIZE)

        for i, chunk in enumerate(entity_chunks):
            logger.debug(f"Processing chunk {i+1}/{len(entity_chunks)} ({len(chunk)} entities)")

            # Compare each entity in the chunk with all other entities
            for j, entity1 in enumerate(chunk):
                for entity2 in entities:
                    # Skip self-comparison
                    if entity1.uuid == entity2.uuid:
                        continue

                    # Calculate similarity
                    similarity_score, match_type = calculate_entity_similarity(entity1, entity2)

                    # If similarity is above threshold, add to matches
                    if similarity_score > 0:
                        match = create_entity_match(entity1, entity2, similarity_score, match_type)
                        matches.append(match)

        processing_time = time.time() - start_time
        logger.info(f"Found {len(matches)} potential duplicate matches in {processing_time:.2f} seconds")

        # Log details about the matches found
        try:
            if matches and VERBOSE_LOGGING:
                logger.debug("Duplicate matches found:")
                for i, match in enumerate(matches[:10]):  # Show first 10 matches
                    logger.debug(f"  {i+1}. {match.source_name} -> {match.target_name} (score: {match.similarity_score:.3f}, type: {match.match_type})")
                if len(matches) > 10:
                    logger.debug(f"  ... and {len(matches) - 10} more matches")
        except NameError:
            pass

        return matches

    async def merge_entities(self, matches: List[EntityMatch]) -> int:
        """
        Merge duplicate entities in the database.

        Args:
            matches: List of entity matches to merge

        Returns:
            Number of entities merged
        """
        adapter = await self.get_database_adapter()
        merged_count = 0

        # Group matches by target entity
        merge_groups = {}
        for match in matches:
            if match.target_uuid not in merge_groups:
                merge_groups[match.target_uuid] = []
            merge_groups[match.target_uuid].append(match.source_uuid)

        # Process each merge group
        for target_uuid, source_uuids in merge_groups.items():
            # Skip empty groups
            if not source_uuids:
                continue

            logger.info(f"Processing merge group: target={target_uuid}, sources={source_uuids}")

            # First, check if we need to use ID-based merging (for entities with missing UUIDs)
            check_query = """
            MATCH (target:Entity {uuid: $target_uuid})
            RETURN ID(target) as target_id
            """

            try:
                check_result = adapter.execute_cypher(check_query, {"target_uuid": target_uuid})

                # If target entity exists with UUID, proceed with UUID-based merging
                if check_result and len(check_result) > 1 and len(check_result[1]) > 0:
                    target_id = check_result[1][0][0]

                    # Build query to merge entities - very simple approach for FalkorDB
                    # Process one source entity at a time to avoid complex UNWIND issues
                    merged_in_group = 0

                    for source_uuid in source_uuids:
                        simple_query = """
                        MATCH (target:Entity {uuid: $target_uuid})
                        MATCH (source:Entity {uuid: $source_uuid})
                        WHERE source <> target

                        // Update target description if source has better one
                        SET target.description = CASE
                            WHEN source.description IS NOT NULL AND (target.description IS NULL OR size(coalesce(target.description, '')) < size(coalesce(source.description, '')))
                            THEN source.description
                            ELSE target.description
                        END

                        // Delete source entity
                        DETACH DELETE source
                        RETURN 1 as deleted_count
                        """

                        try:
                            simple_result = adapter.execute_cypher(simple_query, {
                                "target_uuid": target_uuid,
                                "source_uuid": source_uuid
                            })

                            if simple_result and len(simple_result) > 1 and len(simple_result[1]) > 0:
                                deleted_count = simple_result[1][0][0]
                                merged_in_group += deleted_count
                                logger.debug(f"Merged entity {source_uuid} into {target_uuid}")

                        except Exception as e:
                            logger.error(f"Error merging entity {source_uuid} into {target_uuid}: {e}")
                            continue

                    # Return the count for this group
                    merged_count += merged_in_group
                    logger.info(f"Successfully merged {merged_in_group} entities in group {target_uuid}")

                    # Update match objects for this group
                    for match in matches:
                        if match.source_uuid in source_uuids and match.target_uuid == target_uuid:
                            match.merged = True

                    continue  # Skip the old query execution below
                else:
                    logger.warning(f"Target entity with UUID {target_uuid} not found. Skipping merge.")
                    continue
            except Exception as e:
                logger.error(f"Error checking target entity: {e}")
                continue

        logger.info(f"Merged {merged_count} duplicate entities")
        return merged_count

    async def deduplicate_entities(
        self,
        entity_type: Optional[str] = None,
        document_id: Optional[str] = None,
        entities: Optional[List[Dict[str, Any]]] = None,
        merge: bool = True,
        limit: int = 0
    ) -> DeduplicationResult:
        """
        Find and optionally merge duplicate entities.

        Args:
            entity_type: Type of entities to deduplicate (optional)
            document_id: ID of the document to deduplicate entities from (optional)
            entities: List of entities to deduplicate (optional)
            merge: Whether to merge duplicate entities
            limit: Maximum number of entities to process (0 means no limit)

        Returns:
            Deduplication result
        """
        start_time = time.time()

        # Convert entities to EntityForDeduplication objects if provided
        entity_objects = []
        if entities:
            for entity in entities:
                entity_obj = EntityForDeduplication(
                    uuid=entity.get("uuid", str(uuid.uuid4())),
                    name=entity.get("name", ""),
                    type=entity.get("type", ""),
                    description=entity.get("description"),
                    source_document_id=entity.get("source_document_id"),
                    source_fact_id=entity.get("source_fact_id"),
                    confidence=entity.get("confidence"),
                    attributes=entity.get("attributes"),
                    created_at=entity.get("created_at")
                )
                entity_objects.append(entity_obj)
        else:
            # Fetch entities from database
            entity_objects = await self.fetch_entities(entity_type, document_id, limit)

        # Find duplicates
        matches = await self.find_duplicates(entity_objects)

        # Merge duplicates if requested
        merged_count = 0
        if merge and matches:
            logger.info(f"Attempting to merge {len(matches)} potential duplicate matches")
            merged_count = await self.merge_entities(matches)
            logger.info(f"Successfully merged {merged_count} entities")
        elif not merge:
            logger.info("Merge disabled - skipping entity merging")
        elif not matches:
            logger.info("No duplicate matches found - nothing to merge")

        # Calculate duplicate groups
        duplicate_groups = set()
        for match in matches:
            duplicate_groups.add(match.target_uuid)

        # Create result
        result = DeduplicationResult(
            total_entities=len(entity_objects),
            duplicate_groups=len(duplicate_groups),
            total_duplicates=len(matches),
            merged_entities=merged_count,
            entity_matches=matches,
            processing_time=time.time() - start_time
        )

        return result
