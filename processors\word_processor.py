"""
Microsoft Word Document Processor
Handles .doc and .docx files with enhanced text extraction and metadata.
"""

import os
import asyncio
from typing import Dict, Any, Optional
from pathlib import Path
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class WordProcessor:
    """
    Processor for Microsoft Word documents (.doc, .docx).
    """
    
    def __init__(self):
        """Initialize the Word processor."""
        self.supported_extensions = ['.doc', '.docx']
        
        # Try to import required libraries
        self.docx_available = self._check_docx_availability()
        self.mistral_ocr = self._initialize_mistral_ocr()
    
    def _check_docx_availability(self) -> bool:
        """Check if python-docx is available."""
        try:
            import docx
            return True
        except ImportError:
            logger.warning("python-docx not available. Install with: pip install python-docx")
            return False
    
    def _initialize_mistral_ocr(self):
        """Initialize Mistral OCR if available."""
        try:
            from utils.mistral_ocr import MistralOCRProcessor
            return MistralOCRProcessor()
        except Exception as e:
            logger.warning(f"Mistral OCR not available: {e}")
            return None
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text and metadata from a Word document.
        
        Args:
            file_path: Path to the Word document
            
        Returns:
            Dictionary containing extracted text, metadata, and processing info
        """
        try:
            file_ext = file_path.suffix.lower()
            
            if file_ext not in self.supported_extensions:
                return {
                    'success': False,
                    'error': f"Unsupported file extension: {file_ext}",
                    'text': '',
                    'metadata': {}
                }
            
            # Try Mistral OCR first (supports both .doc and .docx)
            if self.mistral_ocr:
                try:
                    logger.info(f"Extracting text from {file_path} using Mistral OCR")
                    text = await self.mistral_ocr.extract_text_from_word_document(str(file_path))
                    
                    if text and len(text.strip()) > 0:
                        metadata = await self._extract_metadata_mistral(file_path)
                        return {
                            'success': True,
                            'text': text.strip(),
                            'metadata': metadata,
                            'ocr_provider': 'mistral-ocr',
                            'extraction_method': 'mistral_ocr'
                        }
                    else:
                        logger.warning(f"Mistral OCR returned empty text for {file_path}")
                except Exception as e:
                    logger.warning(f"Mistral OCR failed for {file_path}: {e}")
            
            # Fallback to python-docx for .docx files
            if file_ext == '.docx' and self.docx_available:
                try:
                    logger.info(f"Extracting text from {file_path} using python-docx")
                    result = await self._extract_with_docx(file_path)

                    if result['success']:
                        return result
                except Exception as e:
                    logger.warning(f"python-docx extraction failed for {file_path}: {e}")

            # Fallback for .doc files using docx2txt
            if file_ext == '.doc':
                try:
                    logger.info(f"Extracting text from {file_path} using docx2txt")
                    result = await self._extract_with_docx2txt(file_path)

                    if result['success']:
                        return result
                except Exception as e:
                    logger.warning(f"docx2txt extraction failed for {file_path}: {e}")

                # Try antiword as final fallback for .doc files
                try:
                    logger.info(f"Extracting text from {file_path} using antiword")
                    result = await self._extract_with_antiword(file_path)

                    if result['success']:
                        return result
                except Exception as e:
                    logger.warning(f"antiword extraction failed for {file_path}: {e}")

            # If all methods fail
            return {
                'success': False,
                'error': f"Failed to extract text from {file_path}. No suitable extraction method available.",
                'text': '',
                'metadata': {}
            }
            
        except Exception as e:
            logger.error(f"Error processing Word document {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_with_docx(self, file_path: Path) -> Dict[str, Any]:
        """Extract text using python-docx library."""
        try:
            from docx import Document
            
            doc = Document(file_path)
            
            # Extract text from paragraphs
            paragraphs = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    paragraphs.append(paragraph.text.strip())
            
            # Extract text from tables
            table_text = []
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        table_text.append(' | '.join(row_text))
            
            # Combine all text
            all_text = []
            if paragraphs:
                all_text.extend(paragraphs)
            if table_text:
                all_text.append('\n--- Tables ---\n')
                all_text.extend(table_text)
            
            text = '\n'.join(all_text)
            
            # Extract metadata
            metadata = await self._extract_metadata_docx(doc, file_path)
            
            return {
                'success': True,
                'text': text,
                'metadata': metadata,
                'ocr_provider': 'python-docx',
                'extraction_method': 'python_docx',
                'paragraphs_count': len(paragraphs),
                'tables_count': len(doc.tables)
            }
            
        except Exception as e:
            logger.error(f"Error extracting with python-docx: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata_docx(self, doc, file_path: Path) -> Dict[str, Any]:
        """Extract metadata from docx document."""
        try:
            core_props = doc.core_properties
            
            metadata = {
                'title': core_props.title or file_path.stem,
                'author': core_props.author or 'Unknown',
                'subject': core_props.subject or '',
                'keywords': core_props.keywords or '',
                'comments': core_props.comments or '',
                'created': core_props.created.isoformat() if core_props.created else '',
                'modified': core_props.modified.isoformat() if core_props.modified else '',
                'last_modified_by': core_props.last_modified_by or '',
                'revision': core_props.revision or '',
                'category': core_props.category or '',
                'language': core_props.language or '',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }
            
            # Add document statistics
            try:
                metadata.update({
                    'paragraphs_count': len(doc.paragraphs),
                    'tables_count': len(doc.tables),
                    'sections_count': len(doc.sections)
                })
            except Exception as e:
                logger.warning(f"Error extracting document statistics: {e}")
            
            return metadata
            
        except Exception as e:
            logger.warning(f"Error extracting metadata: {e}")
            return {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat()
            }

    async def _extract_with_docx2txt(self, file_path: Path) -> Dict[str, Any]:
        """Extract text from .doc files using docx2txt library (for .docx) or olefile (for .doc)."""
        try:
            # First try with olefile for .doc files
            if file_path.suffix.lower() == '.doc':
                return await self._extract_with_olefile(file_path)

            # For .docx files, use docx2txt
            import docx2txt

            text = docx2txt.process(str(file_path))

            if not text or len(text.strip()) == 0:
                return {
                    'success': False,
                    'error': 'No text extracted using docx2txt',
                    'text': '',
                    'metadata': {}
                }

            # Extract basic metadata
            metadata = {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat(),
                'word_count': len(text.split()),
                'character_count': len(text),
                'extraction_method': 'docx2txt'
            }

            return {
                'success': True,
                'text': text.strip(),
                'metadata': metadata,
                'ocr_provider': 'docx2txt',
                'extraction_method': 'docx2txt'
            }

        except ImportError:
            logger.warning("docx2txt not available. Install with: pip install docx2txt")
            return {
                'success': False,
                'error': 'docx2txt library not available',
                'text': '',
                'metadata': {}
            }
        except Exception as e:
            logger.error(f"Error extracting with docx2txt: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }

    async def _extract_with_olefile(self, file_path: Path) -> Dict[str, Any]:
        """Extract text from .doc files using olefile library."""
        try:
            import olefile

            # Check if it's a valid OLE file
            if not olefile.isOleFile(str(file_path)):
                return {
                    'success': False,
                    'error': 'File is not a valid OLE document',
                    'text': '',
                    'metadata': {}
                }

            # Open the OLE file
            ole = olefile.OleFileIO(str(file_path))

            # Try to extract text from WordDocument stream
            text_content = ""

            if ole._olestream_size is not None:
                # Try to find text streams
                for stream_name in ole.listdir():
                    if 'WordDocument' in str(stream_name) or 'Document' in str(stream_name):
                        try:
                            stream = ole.openstream(stream_name)
                            raw_data = stream.read()

                            # Try to decode as text (this is a simplified approach)
                            # Real .doc parsing is much more complex
                            try:
                                # Try UTF-8 first
                                text_content = raw_data.decode('utf-8', errors='ignore')
                            except:
                                try:
                                    # Try latin-1
                                    text_content = raw_data.decode('latin-1', errors='ignore')
                                except:
                                    # Try cp1252 (Windows)
                                    text_content = raw_data.decode('cp1252', errors='ignore')

                            # Clean up the text (remove null bytes and control characters)
                            text_content = ''.join(char for char in text_content if char.isprintable() or char.isspace())

                            if len(text_content.strip()) > 100:  # If we found substantial text
                                break

                        except Exception as e:
                            logger.warning(f"Error reading stream {stream_name}: {e}")
                            continue

            ole.close()

            if not text_content or len(text_content.strip()) < 50:
                return {
                    'success': False,
                    'error': 'No readable text found in .doc file',
                    'text': '',
                    'metadata': {}
                }

            # Extract basic metadata
            metadata = {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat(),
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'extraction_method': 'olefile'
            }

            return {
                'success': True,
                'text': text_content.strip(),
                'metadata': metadata,
                'ocr_provider': 'olefile',
                'extraction_method': 'olefile'
            }

        except ImportError:
            logger.warning("olefile not available. Install with: pip install olefile")
            return {
                'success': False,
                'error': 'olefile library not available',
                'text': '',
                'metadata': {}
            }
        except Exception as e:
            logger.error(f"Error extracting with olefile: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }

    async def _extract_with_antiword(self, file_path: Path) -> Dict[str, Any]:
        """Extract text from .doc files using antiword command-line tool."""
        try:
            import subprocess

            result = subprocess.run(
                ['antiword', str(file_path)],
                capture_output=True,
                text=True,
                timeout=30
            )

            if result.returncode == 0 and result.stdout:
                text = result.stdout.strip()

                if not text:
                    return {
                        'success': False,
                        'error': 'No text extracted using antiword',
                        'text': '',
                        'metadata': {}
                    }

                # Extract basic metadata
                metadata = {
                    'title': file_path.stem,
                    'author': 'Unknown',
                    'file_size': file_path.stat().st_size,
                    'file_extension': file_path.suffix,
                    'extraction_timestamp': datetime.now().isoformat(),
                    'word_count': len(text.split()),
                    'character_count': len(text),
                    'extraction_method': 'antiword'
                }

                return {
                    'success': True,
                    'text': text,
                    'metadata': metadata,
                    'ocr_provider': 'antiword',
                    'extraction_method': 'antiword'
                }
            else:
                return {
                    'success': False,
                    'error': f'antiword failed with return code {result.returncode}',
                    'text': '',
                    'metadata': {}
                }

        except FileNotFoundError:
            logger.warning("antiword command not found. Install antiword for .doc file support")
            return {
                'success': False,
                'error': 'antiword command not available',
                'text': '',
                'metadata': {}
            }
        except subprocess.TimeoutExpired:
            logger.error(f"antiword timed out processing {file_path}")
            return {
                'success': False,
                'error': 'antiword processing timed out',
                'text': '',
                'metadata': {}
            }
        except Exception as e:
            logger.error(f"Error extracting with antiword: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def _extract_metadata_mistral(self, file_path: Path) -> Dict[str, Any]:
        """Extract basic metadata when using Mistral OCR."""
        try:
            stat = file_path.stat()
            
            return {
                'title': file_path.stem,
                'author': 'Unknown',
                'file_size': stat.st_size,
                'file_extension': file_path.suffix,
                'created': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'extraction_timestamp': datetime.now().isoformat(),
                'extraction_method': 'mistral_ocr'
            }
            
        except Exception as e:
            logger.warning(f"Error extracting basic metadata: {e}")
            return {
                'title': file_path.stem,
                'extraction_timestamp': datetime.now().isoformat()
            }
    
    def is_supported(self, file_path: Path) -> bool:
        """Check if the file is supported by this processor."""
        return file_path.suffix.lower() in self.supported_extensions
    
    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return self.supported_extensions.copy()
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the document content.
        
        Args:
            file_path: Path to the document
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary containing preview information
        """
        try:
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            text = result['text']
            preview_text = text[:max_chars]
            
            if len(text) > max_chars:
                preview_text += "... [truncated]"
            
            return {
                'success': True,
                'preview_text': preview_text,
                'full_length': len(text),
                'metadata': result['metadata'],
                'extraction_method': result.get('extraction_method', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error generating preview for {file_path}: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'full_length': 0
            }
