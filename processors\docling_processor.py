"""
Docling Document Processor
Uses IBM's Docling library for advanced document processing including .doc files.
"""

import asyncio
from typing import Dict, Any
from pathlib import Path
import logging
from datetime import datetime

from utils.logging_utils import get_logger

logger = get_logger(__name__)

class DoclingProcessor:
    """
    Document processor using IBM's Docling library.
    Supports multiple formats including PDF, DOCX, DOC, PPTX, HTML, images, etc.
    """
    
    def __init__(self):
        """Initialize the Docling processor."""
        # Docling natively supports these formats
        self.native_supported = {
            '.pdf', '.docx', '.pptx', '.html', '.htm',
            '.jpg', '.jpeg', '.png', '.tiff', '.tif',
            '.bmp', '.webp', '.xlsx', '.csv', '.md'
        }
        # We can convert these to supported formats
        self.conversion_supported = {
            '.doc'  # Convert .doc to PDF first
        }
        self.supported_extensions = self.native_supported | self.conversion_supported
        
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports the file type."""
        return Path(file_path).suffix.lower() in self.supported_extensions
    
    async def extract_text(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract text from document using Docling.

        Args:
            file_path: Path to the document file

        Returns:
            Dictionary with extraction results
        """
        try:
            logger.info(f"Processing document with Docling: {file_path}")

            # Check if we need to convert the file first
            file_ext = file_path.suffix.lower()
            processing_path = file_path
            temp_file = None

            if file_ext in self.conversion_supported:
                logger.info(f"Converting {file_ext} file to PDF for Docling processing")
                processing_path = await self._convert_to_pdf(file_path)
                if not processing_path:
                    return {
                        'success': False,
                        'error': f'Failed to convert {file_ext} file to PDF',
                        'text': '',
                        'metadata': {}
                    }
                temp_file = processing_path

            # Import Docling components
            from docling.document_converter import DocumentConverter

            # Initialize the converter
            converter = DocumentConverter()

            # Convert the document
            result = converter.convert(str(processing_path))

            # Extract text content
            text_content = result.document.export_to_markdown()

            # Clean up temporary file if created
            if temp_file and temp_file != file_path:
                try:
                    temp_file.unlink()
                except:
                    pass
            
            if not text_content or len(text_content.strip()) == 0:
                return {
                    'success': False,
                    'error': 'No text content extracted from document',
                    'text': '',
                    'metadata': {}
                }
            
            # Extract metadata
            metadata = {
                'title': file_path.stem,
                'file_size': file_path.stat().st_size,
                'file_extension': file_path.suffix,
                'extraction_timestamp': datetime.now().isoformat(),
                'word_count': len(text_content.split()),
                'character_count': len(text_content),
                'extraction_method': 'docling',
                'docling_version': self._get_docling_version()
            }
            
            # Add document-specific metadata if available
            if hasattr(result.document, 'meta'):
                doc_meta = result.document.meta
                if hasattr(doc_meta, 'title') and doc_meta.title:
                    metadata['document_title'] = doc_meta.title
                if hasattr(doc_meta, 'author') and doc_meta.author:
                    metadata['author'] = doc_meta.author
                if hasattr(doc_meta, 'creation_date') and doc_meta.creation_date:
                    metadata['creation_date'] = str(doc_meta.creation_date)
                if hasattr(doc_meta, 'modification_date') and doc_meta.modification_date:
                    metadata['modification_date'] = str(doc_meta.modification_date)
            
            logger.info(f"✅ Successfully extracted {len(text_content)} characters from {file_path}")
            
            return {
                'success': True,
                'text': text_content.strip(),
                'metadata': metadata,
                'ocr_provider': 'docling',
                'extraction_method': 'docling'
            }
            
        except ImportError as e:
            logger.error(f"Docling not available: {e}")
            return {
                'success': False,
                'error': 'Docling library not available. Install with: pip install docling',
                'text': '',
                'metadata': {}
            }
        except Exception as e:
            logger.error(f"Error processing document with Docling: {e}")
            return {
                'success': False,
                'error': str(e),
                'text': '',
                'metadata': {}
            }
    
    async def preview_document(self, file_path: Path, max_chars: int = 1000) -> Dict[str, Any]:
        """
        Generate a preview of the document content.
        
        Args:
            file_path: Path to the document file
            max_chars: Maximum characters to include in preview
            
        Returns:
            Dictionary with preview results
        """
        try:
            # Extract full text first
            result = await self.extract_text(file_path)
            
            if not result['success']:
                return result
            
            # Create preview
            full_text = result['text']
            preview_text = full_text[:max_chars]
            if len(full_text) > max_chars:
                preview_text += "..."
            
            return {
                'success': True,
                'preview_text': preview_text,
                'metadata': result['metadata'],
                'extraction_method': 'docling'
            }
            
        except Exception as e:
            logger.error(f"Error generating document preview: {e}")
            return {
                'success': False,
                'error': str(e),
                'preview_text': '',
                'metadata': {}
            }
    
    def _get_docling_version(self) -> str:
        """Get the version of the Docling library."""
        try:
            import docling
            return getattr(docling, '__version__', 'unknown')
        except:
            return 'unknown'
    
    async def _convert_to_pdf(self, file_path: Path) -> Path:
        """Convert .doc file to PDF for processing."""
        try:
            import tempfile
            import subprocess

            # Create a temporary PDF file
            temp_dir = Path(tempfile.mkdtemp())
            pdf_path = temp_dir / (file_path.stem + '.pdf')

            # Try using LibreOffice to convert .doc to PDF
            try:
                cmd = [
                    'soffice',
                    '--headless',
                    '--convert-to', 'pdf',
                    '--outdir', str(temp_dir),
                    str(file_path)
                ]

                result = subprocess.run(
                    cmd,
                    capture_output=True,
                    text=True,
                    timeout=60
                )

                if result.returncode == 0 and pdf_path.exists():
                    logger.info(f"Successfully converted {file_path} to PDF using LibreOffice")
                    return pdf_path
                else:
                    logger.warning(f"LibreOffice conversion failed: {result.stderr}")

            except FileNotFoundError:
                logger.warning("LibreOffice not found")
            except subprocess.TimeoutExpired:
                logger.warning("LibreOffice conversion timed out")

            # Try using Microsoft Word COM automation (Windows only)
            try:
                import win32com.client

                word = win32com.client.Dispatch("Word.Application")
                word.Visible = False

                try:
                    doc = word.Documents.Open(str(file_path.absolute()))
                    doc.SaveAs2(str(pdf_path.absolute()), FileFormat=17)  # 17 = PDF format
                    doc.Close()

                    logger.info(f"Successfully converted {file_path} to PDF using Word COM")
                    return pdf_path

                finally:
                    word.Quit()

            except ImportError:
                logger.warning("win32com not available")
            except Exception as e:
                logger.warning(f"Word COM conversion failed: {e}")

            # If all conversion methods fail
            logger.error(f"Failed to convert {file_path} to PDF - no conversion method available")
            return None

        except Exception as e:
            logger.error(f"Error in PDF conversion: {e}")
            return None

    def get_supported_extensions(self) -> list:
        """Get list of supported file extensions."""
        return list(self.supported_extensions)
