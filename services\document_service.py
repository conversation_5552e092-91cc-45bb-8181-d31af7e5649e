"""
Document processing service for the Graphiti application.
"""

import os
import uuid
from pathlib import Path
from typing import Dict, Any, Union
from datetime import datetime

from fastapi import UploadFile, HTTPException

from utils.config import UPLOADS_DIR
from utils.logging_utils import get_logger
from utils.file_utils import save_uploaded_file, get_file_category
from utils.text_utils import split_text_recursively
from database.database_service import get_falkordb_adapter, create_episode_node, create_fact_node
from models.document_simplified import (
    DocumentProcessingOptions, DocumentUploadResponse, DocumentType,
    DocumentSummary, DocumentList, DocumentDetails
)

# Import PDF processor
from processors.pdf_processor import PDFProcessor

# Set up logger
logger = get_logger(__name__)

async def process_document(
    file: UploadFile,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a document file.

    Args:
        file: Uploaded file
        options: Processing options

    Returns:
        Processing result
    """
    # Generate a unique ID for the file
    file_id = str(uuid.uuid4())

    # Save the file
    file_content = await file.read()
    file_path = save_uploaded_file(file_content, file.filename, UPLOADS_DIR)

    logger.info(f"File saved: {file_path}")

    # Determine file type
    file_category = get_file_category(file.filename)

    # Process based on file type
    if file_category == 'pdf':
        return await process_pdf_document(file_path, file_id, options)
    elif file_category == 'text':
        return await process_text_document(file_path, file_id, options)
    elif file_category == 'word':
        return await process_word_document(file_path, file_id, options)
    elif file_category == 'html':
        return await process_html_document(file_path, file_id, options)
    else:
        return {
            "error": f"Unsupported file type: {file_category}",
            "success": False
        }

async def process_pdf_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a PDF document.

    Args:
        file_path: Path to the PDF file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing PDF document: {file_path} with chunk_size={options.chunk_size}, overlap={options.overlap}")

    # Process the PDF using the PDF processor
    pdf_processor = PDFProcessor()
    result = await pdf_processor.process_file(
        file_path=str(file_path),
        chunk_size=options.chunk_size,
        overlap=options.overlap
    )

    return result

async def process_text_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a text document.

    Args:
        file_path: Path to the text file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing text document: {file_path}")

    try:
        # Read the text file
        with open(file_path, 'r', encoding='utf-8') as f:
            text = f.read()

        # Split text into chunks
        chunks = split_text_recursively(text, options.chunk_size, options.overlap)

        # Create Episode node
        episode_name = os.path.basename(file_path)
        episode_uuid = await create_episode_node(episode_name, {
            "file_path": str(file_path),
            "file_id": file_id,
            "processed_at": datetime.now().isoformat()
        })

        # Create Fact nodes
        for i, chunk in enumerate(chunks):
            await create_fact_node(chunk, episode_uuid, {
                "chunk_num": i + 1,
                "chunk_size": len(chunk)
            })

        return {
            "success": True,
            "episode_id": episode_uuid,
            "chunks": len(chunks),
            "file_path": str(file_path),
            "file_id": file_id,
            "ocr_provider": "text"
        }

    except Exception as e:
        logger.error(f"Error processing text document: {e}")
        return {
            "error": f"Error processing text document: {str(e)}",
            "success": False
        }

async def process_word_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process a Word document.

    Args:
        file_path: Path to the Word file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing Word document: {file_path}")

    # TODO: Implement Word document processing
    return {
        "error": "Word document processing not yet implemented",
        "success": False
    }

async def process_html_document(
    file_path: Union[str, Path],
    file_id: str,
    options: DocumentProcessingOptions
) -> Dict[str, Any]:
    """
    Process an HTML document.

    Args:
        file_path: Path to the HTML file
        file_id: Unique ID for the file
        options: Processing options

    Returns:
        Processing result
    """
    logger.info(f"Processing HTML document: {file_path}")

    # TODO: Implement HTML document processing
    return {
        "error": "HTML document processing not yet implemented",
        "success": False
    }

async def get_document_list(page: int = 1, page_size: int = 10) -> DocumentList:
    """
    Get a list of documents.

    Args:
        page: Page number
        page_size: Number of documents per page

    Returns:
        DocumentList: List of documents conforming to the DocumentList model

    Raises:
        HTTPException: If there is an error retrieving the documents
    """
    try:
        adapter = await get_falkordb_adapter()

        # Query to get documents
        query = f"""
        MATCH (e:Episode)
        RETURN e.uuid as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at
        ORDER BY e.processed_at DESC
        SKIP {(page - 1) * page_size}
        LIMIT {page_size}
        """

        result = adapter.execute_cypher(query)

        documents = []
        if result and len(result) > 1:
            headers = result[0]
            for row in result[1]:
                doc = {}
                for i, header in enumerate(headers):
                    doc[header] = row[i]

                # Get document details
                doc_details = await get_document_details(doc["uuid"])
                doc.update(doc_details)

                # Format document to match DocumentSummary model
                file_type = doc["file_type"] if doc["file_type"] in [e.value for e in DocumentType] else DocumentType.OTHER

                try:
                    document_summary = DocumentSummary(
                        uuid=doc["uuid"],
                        filename=doc["name"],
                        file_type=file_type,
                        upload_date=datetime.fromisoformat(doc["processed_at"]) if doc["processed_at"] else datetime.now(),
                        chunks=doc.get("chunks", 0),
                        entities=doc.get("entities", 0),
                        references=doc.get("references", 0)
                    )
                    documents.append(document_summary)
                except Exception as validation_error:
                    logger.warning(f"Validation error for document {doc['uuid']}: {str(validation_error)}")
                    # Skip invalid documents instead of failing the entire request
                    continue

        # Get total count
        count_query = """
        MATCH (e:Episode)
        RETURN count(e) as count
        """

        count_result = adapter.execute_cypher(count_query)
        total = 0
        if count_result and len(count_result) > 1 and len(count_result[1]) > 0:
            total = count_result[1][0][0]

        # Create the response data using the Pydantic model
        try:
            document_list = DocumentList(
                documents=documents,
                total=total,
                page=page,
                page_size=page_size
            )
            return document_list
        except Exception as validation_error:
            logger.error(f"Validation error for document list: {str(validation_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error validating document list: {str(validation_error)}"
            )

    except Exception as e:
        logger.error(f"Error getting document list: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document list: {str(e)}")

async def get_document_count() -> int:
    """
    Get the total number of documents.

    Returns:
        Total number of documents
    """
    try:
        adapter = await get_falkordb_adapter()

        query = """
        MATCH (e:Episode)
        RETURN COUNT(e) AS count
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            return result[1][0][0]

        return 0

    except Exception as e:
        logger.error(f"Error getting document count: {str(e)}")
        return 0

async def get_document_details(document_id: str) -> DocumentDetails:
    """
    Get details of a document.

    Args:
        document_id: Document ID

    Returns:
        DocumentDetails: Document details with validated data

    Raises:
        HTTPException: If there is an error retrieving the document details
    """
    try:
        adapter = await get_falkordb_adapter()

        # Query to get document details by episode UUID
        query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})
        OPTIONAL MATCH (e)-[:CONTAINS]->(f:Fact)
        OPTIONAL MATCH (f)-[:MENTIONS]->(entity:Entity)
        RETURN e.uuid as uuid, e.name as name, e.file_path as file_path, e.processed_at as processed_at,
               count(DISTINCT f) as chunks, count(DISTINCT entity) as entities
        """

        result = adapter.execute_cypher(query)

        if result and len(result) > 1 and len(result[1]) > 0:
            headers = result[0]
            row = result[1][0]

            doc = {}
            for i, header in enumerate(headers):
                doc[header] = row[i]

            # Determine file type
            if "file_path" in doc and doc["file_path"]:
                file_type = get_file_category(doc["file_path"])
                # Ensure file_type is a valid enum value
                if file_type not in [e.value for e in DocumentType]:
                    logger.warning(f"Invalid file_type: {file_type}, defaulting to 'other'")
                    file_type = DocumentType.OTHER
            else:
                file_type = DocumentType.OTHER

            # References are handled separately from the graph database (in CSV)
            # So we don't count them here - they're accessed via the /api/references endpoint
            references_count = 0

            # Create the document details using the Pydantic model
            try:
                document_details = DocumentDetails(
                    uuid=doc["uuid"],
                    name=doc["name"],
                    file_path=doc.get("file_path"),
                    processed_at=doc.get("processed_at"),
                    chunks=int(doc.get("chunks", 0)),
                    entities=int(doc.get("entities", 0)),
                    references=int(references_count),
                    file_type=file_type
                )
                return document_details
            except Exception as validation_error:
                logger.error(f"Validation error for document {document_id}: {str(validation_error)}")
                raise HTTPException(
                    status_code=500,
                    detail=f"Error validating document details: {str(validation_error)}"
                )

        # Return default document details for non-existent document
        try:
            default_details = DocumentDetails(
                uuid=document_id,
                name="Unknown document",
                file_path=None,
                processed_at=None,
                chunks=0,
                entities=0,
                references=0,
                file_type=DocumentType.OTHER
            )
            return default_details
        except Exception as validation_error:
            logger.error(f"Validation error for default document details: {str(validation_error)}")
            raise HTTPException(
                status_code=500,
                detail=f"Error creating default document details: {str(validation_error)}"
            )

    except Exception as e:
        logger.error(f"Error getting document details for {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting document details: {str(e)}")

async def delete_document(document_id: str) -> Dict[str, Any]:
    """
    Delete a document and all its associated data.

    Args:
        document_id: Document ID (Episode UUID)

    Returns:
        Deletion result

    Raises:
        HTTPException: If there is an error deleting the document
    """
    try:
        adapter = await get_falkordb_adapter()

        # First check if the document exists
        check_query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})
        RETURN e.name as name, e.file_path as file_path
        """

        check_result = adapter.execute_cypher(check_query)

        if not check_result or len(check_result) <= 1 or len(check_result[1]) == 0:
            raise HTTPException(
                status_code=404,
                detail=f"Document not found: {document_id}"
            )

        document_name = check_result[1][0][0] if check_result[1][0] else "Unknown"
        file_path = check_result[1][0][1] if len(check_result[1][0]) > 1 else None

        logger.info(f"Deleting document: {document_name} (UUID: {document_id})")

        # Delete all relationships and entities associated with this document
        # Step 1: Delete MENTIONS relationships from Facts to Entities
        delete_mentions_query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})-[:CONTAINS]->(f:Fact)-[r:MENTIONS]->(entity:Entity)
        DELETE r
        """
        adapter.execute_cypher(delete_mentions_query)

        # Step 2: Delete Facts and their relationships to the Episode
        delete_facts_query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})-[r:CONTAINS]->(f:Fact)
        DELETE r, f
        """
        adapter.execute_cypher(delete_facts_query)

        # Step 3: Delete the Episode itself
        delete_episode_query = f"""
        MATCH (e:Episode {{uuid: '{document_id}'}})
        DELETE e
        """
        adapter.execute_cypher(delete_episode_query)

        # Step 4: Clean up orphaned entities (entities with no remaining mentions)
        cleanup_orphaned_entities_query = """
        MATCH (entity:Entity)
        WHERE NOT (entity)<-[:MENTIONS]-()
        DELETE entity
        """
        orphaned_result = adapter.execute_cypher(cleanup_orphaned_entities_query)

        # Step 5: Delete associated files if they exist
        deleted_files = []
        if file_path and os.path.exists(file_path):
            try:
                os.remove(file_path)
                deleted_files.append(file_path)
                logger.info(f"Deleted file: {file_path}")
            except Exception as file_error:
                logger.warning(f"Could not delete file {file_path}: {file_error}")

        # Step 6: Clean up Redis embeddings for this document
        try:
            from utils.redis_vector_search import get_redis_vector_search_client
            redis_client = get_redis_vector_search_client()

            # Delete embeddings associated with this document
            fact_keys = redis_client.keys(f"fact:*")
            deleted_embeddings = 0

            for key in fact_keys:
                try:
                    fact_data = redis_client.hgetall(key)
                    if fact_data and fact_data.get('episode_uuid') == document_id:
                        redis_client.delete(key)
                        deleted_embeddings += 1
                except Exception as redis_error:
                    logger.warning(f"Error deleting Redis key {key}: {redis_error}")

            logger.info(f"Deleted {deleted_embeddings} embeddings from Redis")

        except Exception as redis_error:
            logger.warning(f"Could not clean up Redis embeddings: {redis_error}")

        # Step 7: Clean up reference CSV files
        try:
            from utils.config import REFERENCES_DIR
            reference_files = []

            if REFERENCES_DIR.exists():
                # Look for reference files associated with this document
                for ref_file in REFERENCES_DIR.glob(f"{document_id}_references.csv"):
                    try:
                        ref_file.unlink()
                        reference_files.append(str(ref_file))
                        logger.info(f"Deleted reference file: {ref_file}")
                    except Exception as ref_error:
                        logger.warning(f"Could not delete reference file {ref_file}: {ref_error}")

        except Exception as ref_error:
            logger.warning(f"Could not clean up reference files: {ref_error}")

        logger.info(f"Successfully deleted document: {document_name}")

        return {
            "success": True,
            "message": f"Document '{document_name}' deleted successfully",
            "document_id": document_id,
            "document_name": document_name,
            "deleted_files": deleted_files,
            "deleted_embeddings": deleted_embeddings if 'deleted_embeddings' in locals() else 0,
            "deleted_references": reference_files if 'reference_files' in locals() else []
        }

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error deleting document {document_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error deleting document: {str(e)}")
