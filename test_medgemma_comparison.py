#!/usr/bin/env python3
"""
Med<PERSON>em<PERSON> vs Llama 4 Maverick Comparison Test

This script compares entity extraction, reference extraction, and embeddings
between MedGemma (Ollama) and Llama 4 Maverick (OpenRouter) using the
"11 Intestinal Dysbiosis Notes" document.
"""

import asyncio
import logging
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.document_processing_service import DocumentProcessingService
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)

class ModelComparison:
    """Compare different models for entity extraction."""
    
    def __init__(self):
        self.results = {}
        
    async def test_model_configuration(self, model_name: str, provider: str, document_path: str):
        """Test a specific model configuration."""

        logger.info(f"🧪 Testing {model_name} ({provider}) on {Path(document_path).name}")

        # Temporarily update environment variables
        original_entity_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
        original_entity_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
        original_openai_key = os.environ.get('OPENAI_API_KEY')

        try:
            # Set the model configuration
            os.environ['ENTITY_EXTRACTION_MODEL'] = model_name
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = provider

            # For local/Ollama provider, disable OpenAI fallback by removing the key temporarily
            if provider.lower() in ['ollama', 'local']:
                if 'OPENAI_API_KEY' in os.environ:
                    del os.environ['OPENAI_API_KEY']
                logger.info(f"🔧 Configured for Ollama/Local with model: {model_name}")
            else:
                logger.info(f"🔧 Configured for {provider} with model: {model_name}")

            # Create a new document processing service
            service = DocumentProcessingService()

            # Record start time
            start_time = time.time()

            # Process the document
            result = await service.process_document(
                file_path=document_path,
                chunk_size=1200,
                overlap=0,
                extract_entities=True,
                extract_references=True,
                extract_metadata=True,
                generate_embeddings=True
            )

            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time

            # Collect results
            test_result = {
                'model_name': model_name,
                'provider': provider,
                'processing_time': processing_time,
                'success': result.get('success', False),
                'facts_count': result.get('facts_count', 0),
                'entities_count': result.get('entities_count', 0),
                'references_count': result.get('references_count', 0),
                'embeddings_count': result.get('embeddings_count', 0),
                'episode_id': result.get('episode_id'),
                'error': result.get('error') if not result.get('success', False) else None
            }

            logger.info(f"✅ {model_name} completed in {processing_time:.2f}s")
            logger.info(f"   Facts: {test_result['facts_count']}")
            logger.info(f"   Entities: {test_result['entities_count']}")
            logger.info(f"   References: {test_result['references_count']}")
            logger.info(f"   Embeddings: {test_result['embeddings_count']}")

            return test_result

        except Exception as e:
            logger.error(f"❌ Error testing {model_name}: {e}")
            return {
                'model_name': model_name,
                'provider': provider,
                'processing_time': 0,
                'success': False,
                'error': str(e)
            }

        finally:
            # Restore original environment variables
            if original_entity_model:
                os.environ['ENTITY_EXTRACTION_MODEL'] = original_entity_model
            if original_entity_provider:
                os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_entity_provider
            if original_openai_key:
                os.environ['OPENAI_API_KEY'] = original_openai_key

    async def run_comparison(self, document_path: str):
        """Run comparison between models."""
        
        logger.info("🚀 Starting MedGemma vs Llama 4 Maverick comparison")
        logger.info(f"📄 Document: {Path(document_path).name}")
        
        # Test configurations
        test_configs = [
            {
                'name': 'Llama 4 Maverick (OpenRouter)',
                'model': 'meta-llama/llama-4-maverick',
                'provider': 'openrouter'
            },
            {
                'name': 'MedGemma 4B (Ollama)',
                'model': 'alibayram/medgemma:4b',
                'provider': 'local'
            }
        ]
        
        results = []
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {config['name']} ({i+1}/{len(test_configs)})")
            logger.info(f"{'='*60}")

            result = await self.test_model_configuration(
                config['model'],
                config['provider'],
                document_path
            )

            result['config_name'] = config['name']
            results.append(result)

            # Add a small delay between tests to allow cleanup
            logger.info(f"⏳ Waiting 5 seconds before next test...")
            await asyncio.sleep(5)
        
        # Generate comparison report
        self.generate_comparison_report(results, document_path)
        
        return results

    def generate_comparison_report(self, results: list, document_path: str):
        """Generate a detailed comparison report."""
        
        logger.info(f"\n{'='*80}")
        logger.info("📊 COMPARISON REPORT")
        logger.info(f"{'='*80}")
        logger.info(f"Document: {Path(document_path).name}")
        logger.info(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Create comparison table
        logger.info(f"\n{'Model':<30} {'Time (s)':<10} {'Facts':<8} {'Entities':<10} {'Refs':<8} {'Embeds':<8} {'Status':<10}")
        logger.info("-" * 90)
        
        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            logger.info(
                f"{result['config_name']:<30} "
                f"{result['processing_time']:<10.2f} "
                f"{result.get('facts_count', 0):<8} "
                f"{result.get('entities_count', 0):<10} "
                f"{result.get('references_count', 0):<8} "
                f"{result.get('embeddings_count', 0):<8} "
                f"{status:<10}"
            )
        
        # Detailed analysis
        logger.info(f"\n📈 DETAILED ANALYSIS:")
        
        successful_results = [r for r in results if r['success']]
        
        if len(successful_results) >= 2:
            llama_result = next((r for r in successful_results if 'llama' in r['model_name'].lower()), None)
            medgemma_result = next((r for r in successful_results if 'medgemma' in r['model_name'].lower()), None)
            
            if llama_result and medgemma_result:
                logger.info(f"\n🔍 Entity Extraction Comparison:")
                logger.info(f"   Llama 4 Maverick: {llama_result.get('entities_count', 0)} entities")
                logger.info(f"   MedGemma 4B:      {medgemma_result.get('entities_count', 0)} entities")
                
                entity_diff = medgemma_result.get('entities_count', 0) - llama_result.get('entities_count', 0)
                if entity_diff > 0:
                    logger.info(f"   📈 MedGemma extracted {entity_diff} more entities (+{entity_diff/llama_result.get('entities_count', 1)*100:.1f}%)")
                elif entity_diff < 0:
                    logger.info(f"   📉 MedGemma extracted {abs(entity_diff)} fewer entities ({entity_diff/llama_result.get('entities_count', 1)*100:.1f}%)")
                else:
                    logger.info(f"   ⚖️  Both models extracted the same number of entities")
                
                logger.info(f"\n⏱️  Performance Comparison:")
                logger.info(f"   Llama 4 Maverick: {llama_result['processing_time']:.2f}s")
                logger.info(f"   MedGemma 4B:      {medgemma_result['processing_time']:.2f}s")
                
                time_diff = medgemma_result['processing_time'] - llama_result['processing_time']
                if time_diff > 0:
                    logger.info(f"   🐌 MedGemma was {time_diff:.2f}s slower")
                else:
                    logger.info(f"   🚀 MedGemma was {abs(time_diff):.2f}s faster")
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"medgemma_comparison_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump({
                'document': str(document_path),
                'test_date': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)
        
        logger.info(f"\n💾 Results saved to: {results_file}")

async def main():
    """Main function."""
    
    # Find the intestinal dysbiosis document
    document_path = None
    
    # Check uploads directory
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for file in uploads_dir.glob("*intestinal*dysbiosis*"):
            document_path = str(file)
            break
        
        if not document_path:
            for file in uploads_dir.glob("*11*intestinal*"):
                document_path = str(file)
                break
    
    # If not found in uploads, check other common directories
    if not document_path:
        for search_dir in ["documents", ".", "test_documents"]:
            search_path = Path(search_dir)
            if search_path.exists():
                for file in search_path.glob("*intestinal*dysbiosis*"):
                    document_path = str(file)
                    break
                if document_path:
                    break
    
    if not document_path:
        logger.error("❌ Could not find '11 Intestinal Dysbiosis Notes' document")
        logger.info("Please ensure the document is in the uploads/ directory")
        return
    
    logger.info(f"📄 Found document: {document_path}")
    
    # Run the comparison
    comparison = ModelComparison()
    results = await comparison.run_comparison(document_path)
    
    logger.info("\n🎉 Comparison completed!")

if __name__ == "__main__":
    asyncio.run(main())
