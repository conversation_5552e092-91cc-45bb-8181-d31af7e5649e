#!/usr/bin/env python3
"""
MedGemma (Google AI) vs Llama 4 Maverick Comparison Test

This script compares entity extraction between:
1. Llama 4 <PERSON><PERSON><PERSON> (OpenRouter) 
2. MedGemma via Google AI Studio API
3. Gemini 1.5 Flash (Google AI) as baseline

Tests are performed on the "11 Intestinal Dysbiosis Notes" document.
"""

import asyncio
import logging
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.document_processing_service import DocumentProcessingService
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)

class GoogleMedGemmaComparison:
    """Compare different models for entity extraction including Google AI models."""
    
    def __init__(self):
        self.results = {}
        
    async def test_model_configuration(self, model_name: str, provider: str, document_path: str):
        """Test a specific model configuration."""
        
        logger.info(f"🧪 Testing {model_name} ({provider}) on {Path(document_path).name}")
        
        # Temporarily update environment variables
        original_entity_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
        original_entity_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
        original_openai_key = os.environ.get('OPENAI_API_KEY')
        
        try:
            # Set the model configuration
            os.environ['ENTITY_EXTRACTION_MODEL'] = model_name
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = provider
            
            # For local/Ollama provider, disable OpenAI fallback
            if provider.lower() in ['ollama', 'local']:
                if 'OPENAI_API_KEY' in os.environ:
                    del os.environ['OPENAI_API_KEY']
                logger.info(f"🔧 Configured for Ollama/Local with model: {model_name}")
            else:
                logger.info(f"🔧 Configured for {provider} with model: {model_name}")
            
            # Create a new document processing service
            service = DocumentProcessingService()
            
            # Record start time
            start_time = time.time()
            
            # Process the document
            result = await service.process_document(
                file_path=document_path,
                chunk_size=1200,
                overlap=0,
                extract_entities=True,
                extract_references=True,
                extract_metadata=True,
                generate_embeddings=True
            )
            
            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Collect results
            test_result = {
                'model_name': model_name,
                'provider': provider,
                'processing_time': processing_time,
                'success': result.get('success', False),
                'facts_count': result.get('facts_count', 0),
                'entities_count': result.get('entities_count', 0),
                'references_count': result.get('references_count', 0),
                'embeddings_count': result.get('embeddings_count', 0),
                'episode_id': result.get('episode_id'),
                'error': result.get('error') if not result.get('success', False) else None
            }
            
            logger.info(f"✅ {model_name} completed in {processing_time:.2f}s")
            logger.info(f"   Facts: {test_result['facts_count']}")
            logger.info(f"   Entities: {test_result['entities_count']}")
            logger.info(f"   References: {test_result['references_count']}")
            logger.info(f"   Embeddings: {test_result['embeddings_count']}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Error testing {model_name}: {e}")
            return {
                'model_name': model_name,
                'provider': provider,
                'processing_time': 0,
                'success': False,
                'error': str(e)
            }
            
        finally:
            # Restore original environment variables
            if original_entity_model:
                os.environ['ENTITY_EXTRACTION_MODEL'] = original_entity_model
            if original_entity_provider:
                os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_entity_provider
            if original_openai_key:
                os.environ['OPENAI_API_KEY'] = original_openai_key

    async def run_comparison(self, document_path: str):
        """Run comparison between models."""
        
        logger.info("🚀 Starting Google AI MedGemma vs Llama 4 Maverick comparison")
        logger.info(f"📄 Document: {Path(document_path).name}")
        
        # Test configurations
        test_configs = [
            {
                'name': 'Llama 4 Maverick (OpenRouter)',
                'model': 'meta-llama/llama-4-maverick',
                'provider': 'openrouter'
            },
            {
                'name': 'Gemini 1.5 Flash (Google AI)',
                'model': 'gemini-1.5-flash',
                'provider': 'google'
            },
            {
                'name': 'Gemini 1.5 Pro (Google AI)',
                'model': 'gemini-1.5-pro',
                'provider': 'google'
            }
        ]
        
        results = []
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {config['name']} ({i+1}/{len(test_configs)})")
            logger.info(f"{'='*60}")
            
            result = await self.test_model_configuration(
                config['model'],
                config['provider'],
                document_path
            )
            
            result['config_name'] = config['name']
            results.append(result)
            
            # Add a small delay between tests to allow cleanup
            logger.info(f"⏳ Waiting 5 seconds before next test...")
            await asyncio.sleep(5)
        
        # Generate comparison report
        self.generate_comparison_report(results, document_path)
        
        return results

    def generate_comparison_report(self, results: list, document_path: str):
        """Generate a detailed comparison report."""
        
        logger.info(f"\n{'='*80}")
        logger.info("📊 GOOGLE AI COMPARISON REPORT")
        logger.info(f"{'='*80}")
        logger.info(f"Document: {Path(document_path).name}")
        logger.info(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Create comparison table
        logger.info(f"\n{'Model':<35} {'Time (s)':<10} {'Facts':<8} {'Entities':<10} {'Refs':<8} {'Embeds':<8} {'Status':<10}")
        logger.info("-" * 95)
        
        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            logger.info(
                f"{result['config_name']:<35} "
                f"{result['processing_time']:<10.2f} "
                f"{result.get('facts_count', 0):<8} "
                f"{result.get('entities_count', 0):<10} "
                f"{result.get('references_count', 0):<8} "
                f"{result.get('embeddings_count', 0):<8} "
                f"{status:<10}"
            )
        
        # Detailed analysis
        logger.info(f"\n📈 DETAILED ANALYSIS:")
        
        successful_results = [r for r in results if r['success']]
        
        if len(successful_results) >= 2:
            # Find best performing models
            best_entities = max(successful_results, key=lambda x: x.get('entities_count', 0))
            fastest = min(successful_results, key=lambda x: x['processing_time'])
            
            logger.info(f"\n🏆 Best Entity Extraction: {best_entities['config_name']}")
            logger.info(f"   Entities: {best_entities.get('entities_count', 0)}")
            
            logger.info(f"\n⚡ Fastest Processing: {fastest['config_name']}")
            logger.info(f"   Time: {fastest['processing_time']:.2f}s")
            
            # Compare Google AI models vs OpenRouter
            google_results = [r for r in successful_results if r['provider'] == 'google']
            openrouter_results = [r for r in successful_results if r['provider'] == 'openrouter']
            
            if google_results and openrouter_results:
                logger.info(f"\n🔍 Google AI vs OpenRouter Comparison:")
                
                avg_google_entities = sum(r.get('entities_count', 0) for r in google_results) / len(google_results)
                avg_openrouter_entities = sum(r.get('entities_count', 0) for r in openrouter_results) / len(openrouter_results)
                
                logger.info(f"   Google AI Average Entities: {avg_google_entities:.1f}")
                logger.info(f"   OpenRouter Average Entities: {avg_openrouter_entities:.1f}")
                
                if avg_google_entities > avg_openrouter_entities:
                    diff = avg_google_entities - avg_openrouter_entities
                    logger.info(f"   📈 Google AI extracted {diff:.1f} more entities on average")
                elif avg_openrouter_entities > avg_google_entities:
                    diff = avg_openrouter_entities - avg_google_entities
                    logger.info(f"   📈 OpenRouter extracted {diff:.1f} more entities on average")
                else:
                    logger.info(f"   ⚖️  Similar entity extraction performance")
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"google_ai_comparison_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump({
                'document': str(document_path),
                'test_date': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)
        
        logger.info(f"\n💾 Results saved to: {results_file}")

async def main():
    """Main function."""
    
    # Find the intestinal dysbiosis document
    document_path = None
    
    # Check uploads directory
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for file in uploads_dir.glob("*intestinal*dysbiosis*"):
            document_path = str(file)
            break
        
        if not document_path:
            for file in uploads_dir.glob("*11*intestinal*"):
                document_path = str(file)
                break
    
    if not document_path:
        logger.error("❌ Could not find '11 Intestinal Dysbiosis Notes' document")
        logger.info("Please ensure the document is in the uploads/ directory")
        return
    
    logger.info(f"📄 Found document: {document_path}")
    
    # Test Google AI client first
    logger.info("🧪 Testing Google AI client...")
    try:
        from utils.google_ai_client import GoogleAIClient
        client = GoogleAIClient()
        test_response = client.generate_completion(
            system_prompt="You are a helpful assistant.",
            user_prompt="Say hello!",
            temperature=0.3,
            max_tokens=50
        )
        logger.info(f"✅ Google AI client working: {test_response[:50]}...")
    except Exception as e:
        logger.error(f"❌ Google AI client test failed: {e}")
        logger.info("Please check your GOOGLE_AI_STUDIO_API_KEY or GEMINI_API_KEY")
        return
    
    # Run the comparison
    comparison = GoogleMedGemmaComparison()
    results = await comparison.run_comparison(document_path)
    
    logger.info("\n🎉 Google AI comparison completed!")

if __name__ == "__main__":
    asyncio.run(main())
