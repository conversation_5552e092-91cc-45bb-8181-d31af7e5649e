#!/usr/bin/env python3
"""
Test the reference extraction system to see if robust extractor is working.
"""

import asyncio
from pathlib import Path
from services.reference_processor import ReferenceProcessor

async def test_reference_system():
    """Test the reference extraction system."""
    
    uploads_dir = Path("uploads")
    if not uploads_dir.exists():
        print("❌ No uploads directory found")
        return
    
    pdf_files = list(uploads_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in uploads")
        return
    
    # Test with the first PDF
    test_file = pdf_files[0]
    print(f"\n🔍 Testing reference extraction system on: {test_file.name}")
    
    try:
        processor = ReferenceProcessor()
        result = await processor.extract_references_from_document(str(test_file))
        
        if result.get("success", False):
            ref_count = result.get("total_reference_count", 0)
            print(f"🎉 SUCCESS: Found {ref_count} references")
            
            # Check if robust extractor was used
            if "robust" in str(result):
                print("✅ Robust extractor was used!")
            else:
                print("⚠️ Robust extractor may not have been used")
                
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
            
        print(f"\nFull result: {result}")
    
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(test_reference_system())
