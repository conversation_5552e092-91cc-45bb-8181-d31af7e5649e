<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Connection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        .info { background: #e3f2fd; color: #1565c0; }
    </style>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    
    <div>
        <button onclick="testWebSocket()">Test WebSocket Connection</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>
    
    <div id="logs"></div>
    
    <script>
        let ws = null;
        
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const logEntry = document.createElement('div');
            logEntry.className = `log ${type}`;
            logEntry.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            logsDiv.appendChild(logEntry);
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }
        
        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }
        
        function testWebSocket() {
            if (ws) {
                ws.close();
            }
            
            // Test with a dummy operation ID
            const testOperationId = 'test-' + Date.now();
            const wsUrl = `ws://${window.location.host}/ws/progress/${testOperationId}`;
            
            log(`Attempting to connect to: ${wsUrl}`, 'info');
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                log('✅ WebSocket connection opened successfully!', 'success');
                
                // Send a test message
                const testMessage = {
                    type: 'request_status',
                    operation_id: testOperationId
                };
                ws.send(JSON.stringify(testMessage));
                log(`📤 Sent test message: ${JSON.stringify(testMessage)}`, 'info');
            };
            
            ws.onmessage = function(event) {
                log(`📥 Received message: ${event.data}`, 'success');
                try {
                    const data = JSON.parse(event.data);
                    log(`📋 Parsed data: ${JSON.stringify(data, null, 2)}`, 'info');
                } catch (e) {
                    log(`⚠️ Could not parse message as JSON: ${e.message}`, 'error');
                }
            };
            
            ws.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`, 'error');
            };
            
            ws.onclose = function(event) {
                log(`🔌 WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'info');
            };
            
            // Test timeout
            setTimeout(() => {
                if (ws && ws.readyState === WebSocket.CONNECTING) {
                    log('⏰ Connection timeout - closing WebSocket', 'error');
                    ws.close();
                }
            }, 5000);
        }
        
        // Test on page load
        window.onload = function() {
            log('🚀 WebSocket test page loaded', 'info');
            log(`🌐 Current host: ${window.location.host}`, 'info');
        };
    </script>
</body>
</html>
