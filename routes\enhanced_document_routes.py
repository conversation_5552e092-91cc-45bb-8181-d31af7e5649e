"""
Enhanced Document Processing Routes
Provides advanced document upload and processing with real-time progress tracking.
"""

import asyncio
import uuid
from typing import List, Dict, Any, Optional
from pathlib import Path
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from processors.enhanced_document_processor import EnhancedDocumentProcessor
from utils.progress_tracker import ProgressTracker
from utils.file_utils import save_uploaded_file, is_supported_file, get_file_category
from utils.logging_utils import get_logger
from utils.config import get_config

logger = get_logger(__name__)
router = APIRouter()

# Global instances
enhanced_processor = EnhancedDocumentProcessor()
active_operations = {}  # Store active progress trackers

# Import WebSocket manager
from utils.websocket_manager import get_websocket_manager

class DocumentUploadRequest(BaseModel):
    """Request model for document upload."""
    chunk_size: int = 1200
    overlap: int = 0
    extract_entities: bool = True
    extract_references: bool = True
    extract_metadata: bool = True
    generate_embeddings: bool = True

class DocumentUploadResponse(BaseModel):
    """Response model for document upload."""
    operation_id: str
    filename: str
    file_type: str
    status: str
    message: str
    supported_extensions: List[str]

class ProgressResponse(BaseModel):
    """Response model for progress tracking."""
    operation_id: str
    document_name: str
    progress_percentage: int
    status: str
    current_step_name: str
    estimated_remaining_time: float
    error_message: Optional[str] = None

class DocumentPreviewResponse(BaseModel):
    """Response model for document preview."""
    filename: str
    file_type: str
    preview_text: str
    metadata: Dict[str, Any]
    extraction_method: str

@router.post("/enhanced-upload", response_model=DocumentUploadResponse)
async def enhanced_upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    generate_embeddings: bool = Form(True)
):
    """
    Enhanced document upload with real-time progress tracking.

    Supports multiple document types with unified processing pipeline.
    """
    try:
        # Validate file type
        if not is_supported_file(file.filename):
            supported_exts = enhanced_processor.get_supported_extensions()
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Supported extensions: {', '.join(supported_exts)}"
            )

        # Validate file size
        config = get_config()
        max_file_size = config.get('system_settings', {}).get('max_file_size', 50) * 1024 * 1024  # Convert MB to bytes

        # Read file content to check size
        file_content = await file.read()
        if len(file_content) > max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File size ({len(file_content) / 1024 / 1024:.1f} MB) exceeds maximum allowed size ({max_file_size / 1024 / 1024:.0f} MB)"
            )

        # Generate operation ID
        operation_id = str(uuid.uuid4())

        # Save uploaded file (file_content already read for size validation)
        file_path = save_uploaded_file(
            file_content,
            file.filename,
            config['paths']['uploads_dir']
        )

        # Get file category
        file_category = get_file_category(file.filename)

        # Start background processing
        background_tasks.add_task(
            process_document_enhanced,
            operation_id,
            file_path,
            file.filename,
            chunk_size,
            overlap,
            extract_entities,
            extract_references,
            extract_metadata,
            generate_embeddings
        )

        logger.info(f"Started enhanced processing for {file.filename} with operation ID {operation_id}")

        return DocumentUploadResponse(
            operation_id=operation_id,
            filename=file.filename,
            file_type=file_category,
            status="processing",
            message="Document upload successful. Processing started in background.",
            supported_extensions=enhanced_processor.get_supported_extensions()
        )

    except Exception as e:
        logger.error(f"Error in enhanced document upload: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/batch-enhanced-upload")
async def batch_enhanced_upload(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(...),
    chunk_size: int = Form(1200),
    overlap: int = Form(0),
    extract_entities: bool = Form(True),
    extract_references: bool = Form(True),
    extract_metadata: bool = Form(True),
    generate_embeddings: bool = Form(True)
):
    """
    Enhanced batch document upload with individual progress tracking.
    """
    try:
        config = get_config()
        results = []

        for file in files:
            try:
                # Validate file type
                if not is_supported_file(file.filename):
                    results.append({
                        "filename": file.filename,
                        "status": "error",
                        "error": f"Unsupported file type: {Path(file.filename).suffix}"
                    })
                    continue

                # Validate file size
                max_file_size = config.get('system_settings', {}).get('max_file_size', 50) * 1024 * 1024  # Convert MB to bytes

                # Read file content to check size
                file_content = await file.read()
                if len(file_content) > max_file_size:
                    results.append({
                        "filename": file.filename,
                        "status": "error",
                        "error": f"File size ({len(file_content) / 1024 / 1024:.1f} MB) exceeds maximum allowed size ({max_file_size / 1024 / 1024:.0f} MB)"
                    })
                    continue

                # Generate operation ID
                operation_id = str(uuid.uuid4())

                # Save uploaded file (file_content already read for size validation)
                file_path = save_uploaded_file(
                    file_content,
                    file.filename,
                    config['paths']['uploads_dir']
                )

                # Get file category
                file_category = get_file_category(file.filename)

                # Start background processing
                background_tasks.add_task(
                    process_document_enhanced,
                    operation_id,
                    file_path,
                    file.filename,
                    chunk_size,
                    overlap,
                    extract_entities,
                    extract_references,
                    extract_metadata,
                    generate_embeddings
                )

                results.append({
                    "operation_id": operation_id,
                    "filename": file.filename,
                    "file_type": file_category,
                    "status": "processing",
                    "message": "Processing started"
                })

            except Exception as e:
                logger.error(f"Error processing file {file.filename}: {e}")
                results.append({
                    "filename": file.filename,
                    "status": "error",
                    "error": str(e)
                })

        return {
            "total_files": len(files),
            "successful_uploads": len([r for r in results if r.get("status") == "processing"]),
            "failed_uploads": len([r for r in results if r.get("status") == "error"]),
            "results": results
        }

    except Exception as e:
        logger.error(f"Error in batch enhanced upload: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/progress/{operation_id}", response_model=ProgressResponse)
async def get_processing_progress(operation_id: str):
    """
    Get real-time processing progress for a document.
    """
    try:
        # First check if operation is still active
        if operation_id in active_operations:
            tracker = active_operations[operation_id]
            progress_data = tracker.get_progress_data()

            return ProgressResponse(
                operation_id=operation_id,
                document_name=progress_data['document_name'],
                progress_percentage=progress_data['progress_percentage'],
                status=progress_data['status'],
                current_step_name=progress_data['current_step_name'],
                estimated_remaining_time=progress_data['statistics']['estimated_remaining_time'],
                error_message=progress_data['error_message']
            )

        # If not active, check persistent storage for final results
        from utils.processing_results_store import get_results_store
        results_store = get_results_store()
        final_results = results_store.get_results(operation_id)

        if final_results:
            # Return the final results in ProgressResponse format
            return ProgressResponse(
                operation_id=operation_id,
                document_name=final_results.get('document_name', 'Unknown'),
                progress_percentage=final_results.get('progress_percentage', 100),
                status=final_results.get('status', 'completed'),
                current_step_name=final_results.get('step_name', 'Processing complete'),
                estimated_remaining_time=0,
                error_message=final_results.get('error_message')
            )

        # If not found anywhere, return 404
        raise HTTPException(status_code=404, detail="Operation not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting progress for operation {operation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/progress/{operation_id}/detailed")
async def get_detailed_progress(operation_id: str):
    """
    Get detailed processing progress including step history.
    """
    try:
        # First check if operation is still active
        if operation_id in active_operations:
            tracker = active_operations[operation_id]
            return tracker.get_progress_data()

        # If not active, check persistent storage for final results
        from utils.processing_results_store import get_results_store
        results_store = get_results_store()
        final_results = results_store.get_results(operation_id)

        if final_results:
            # Return the final results in the expected format
            return final_results

        # If not found anywhere, return 404
        raise HTTPException(status_code=404, detail="Operation not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting detailed progress for operation {operation_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/preview", response_model=DocumentPreviewResponse)
async def preview_document(file: UploadFile = File(...)):
    """
    Generate a preview of document content without full processing.
    """
    try:
        # Validate file type
        if not is_supported_file(file.filename):
            supported_exts = enhanced_processor.get_supported_extensions()
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type. Supported extensions: {', '.join(supported_exts)}"
            )

        # Save temporary file
        config = get_config()
        file_content = await file.read()
        temp_file_path = save_uploaded_file(
            file_content,
            f"preview_{file.filename}",
            config['paths']['uploads_dir']
        )

        try:
            # Get file category and processor
            file_category = get_file_category(file.filename)
            processor = enhanced_processor.processors.get(file_category)

            if not processor:
                raise HTTPException(
                    status_code=400,
                    detail=f"No processor available for file type: {file_category}"
                )

            # Generate preview
            preview_result = await processor.preview_document(temp_file_path, max_chars=1000)

            if not preview_result['success']:
                raise HTTPException(status_code=400, detail=preview_result['error'])

            return DocumentPreviewResponse(
                filename=file.filename,
                file_type=file_category,
                preview_text=preview_result['preview_text'],
                metadata=preview_result['metadata'],
                extraction_method=preview_result['extraction_method']
            )

        finally:
            # Clean up temporary file
            try:
                temp_file_path.unlink()
            except:
                pass

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating preview for {file.filename}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/supported-types")
async def get_supported_types():
    """
    Get information about supported document types.
    """
    try:
        return {
            "supported_extensions": enhanced_processor.get_supported_extensions(),
            "supported_categories": enhanced_processor.get_supported_categories(),
            "processors": {
                category: processor.__class__.__name__
                for category, processor in enhanced_processor.processors.items()
            }
        }

    except Exception as e:
        logger.error(f"Error getting supported types: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/recent-operations")
async def get_recent_operations(limit: int = 10):
    """Get recent enhanced processing operations."""
    try:
        from utils.processing_results_store import ProcessingResultsStore

        store = ProcessingResultsStore()
        operations = store.list_recent_results(limit=limit)

        return {
            "operations": operations,
            "total": len(operations)
        }

    except Exception as e:
        logger.error(f"Error getting recent operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_document_enhanced(
    operation_id: str,
    file_path: Path,
    filename: str,
    chunk_size: int,
    overlap: int,
    extract_entities: bool,
    extract_references: bool,
    extract_metadata: bool,
    generate_embeddings: bool
):
    """
    Background task for enhanced document processing with progress tracking.
    """
    try:
        # Get WebSocket manager
        websocket_manager = get_websocket_manager()

        # Create progress tracker with WebSocket integration
        tracker = ProgressTracker(
            total_steps=7,
            document_name=filename,
            operation_id=operation_id,
            websocket_manager=websocket_manager
        )
        active_operations[operation_id] = tracker

        # Progress callback function
        async def progress_callback(progress_data):
            # Progress is automatically updated in the tracker
            pass

        # Process the document
        result = await enhanced_processor.process_document(
            file_path=file_path,
            chunk_size=chunk_size,
            overlap=overlap,
            extract_entities=extract_entities,
            extract_references=extract_references,
            extract_metadata=extract_metadata,
            generate_embeddings=generate_embeddings,
            progress_callback=progress_callback,
            progress_tracker=tracker
        )

        # Mark as completed
        if result['success']:
            tracker.complete(result)
            logger.info(f"Enhanced processing completed for {filename}")
        else:
            tracker.fail(result.get('error', 'Unknown error'))
            logger.error(f"Enhanced processing failed for {filename}: {result.get('error')}")

    except Exception as e:
        error_msg = f"Error in enhanced processing for {filename}: {str(e)}"
        logger.error(error_msg, exc_info=True)  # Include stack trace

        # Get WebSocket manager for error notification
        websocket_manager = get_websocket_manager()

        if operation_id in active_operations:
            tracker = active_operations[operation_id]

            # Determine error type for better handling
            if isinstance(e, FileNotFoundError):
                error_type = "file_not_found"
                user_message = f"File not found: {filename}"
            elif isinstance(e, PermissionError):
                error_type = "permission_error"
                user_message = f"Permission denied accessing: {filename}"
            elif isinstance(e, MemoryError):
                error_type = "memory_error"
                user_message = f"Insufficient memory to process: {filename}"
            elif "timeout" in str(e).lower():
                error_type = "timeout_error"
                user_message = f"Processing timeout for: {filename}"
            elif "network" in str(e).lower() or "connection" in str(e).lower():
                error_type = "network_error"
                user_message = f"Network error processing: {filename}"
            else:
                error_type = "processing_error"
                user_message = f"Processing error for: {filename}"

            # Update tracker with detailed error info
            tracker.fail(user_message, {
                'error_type': error_type,
                'original_error': str(e),
                'filename': filename,
                'operation_id': operation_id
            })

    finally:
        # Clean up after some time (keep for 1 hour for status checking)
        await asyncio.sleep(3600)  # 1 hour
        if operation_id in active_operations:
            del active_operations[operation_id]

@router.get("/active-operations")
async def get_active_operations():
    """
    Get all active processing operations.
    """
    try:
        operations = {}
        for operation_id, tracker in active_operations.items():
            operations[operation_id] = tracker.get_progress_data()

        return {
            "active_operations": operations,
            "total_active": len(operations)
        }

    except Exception as e:
        logger.error(f"Error getting active operations: {e}")
        raise HTTPException(status_code=500, detail=str(e))
