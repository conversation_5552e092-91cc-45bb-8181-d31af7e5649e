#!/usr/bin/env python3
"""
Test the robust reference extractor.
"""

import asyncio
from pathlib import Path
from services.robust_reference_extractor import RobustReferenceExtractor

async def test_robust_extractor():
    """Test the robust reference extractor on uploaded PDFs."""
    
    uploads_dir = Path("uploads")
    if not uploads_dir.exists():
        print("❌ No uploads directory found")
        return
    
    pdf_files = list(uploads_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in uploads")
        return
    
    # Test with the first PDF
    test_file = pdf_files[0]
    print(f"\n🔍 Testing robust reference extraction on: {test_file.name}")
    
    try:
        extractor = RobustReferenceExtractor()
        result = await extractor.extract_references_from_document(str(test_file))
        
        if result.get("success", False):
            ref_count = result.get("total_reference_count", 0)
            methods = result.get("extraction_methods", [])
            
            print(f"🎉 SUCCESS: Found {ref_count} references using methods: {methods}")
            
            # Show first few references
            references = result.get("references", [])
            for i, ref in enumerate(references[:5]):
                print(f"  {i+1}. {ref.get('text', '')[:100]}...")
        else:
            print(f"❌ FAILED: {result.get('error', 'Unknown error')}")
    
    except Exception as e:
        print(f"❌ ERROR: {e}")

if __name__ == "__main__":
    asyncio.run(test_robust_extractor())
