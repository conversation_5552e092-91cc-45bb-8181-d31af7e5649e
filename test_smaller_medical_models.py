#!/usr/bin/env python3
"""
Test smaller medical models that download faster than II-Medical-8B-1706

Alternative medical models to test:
1. microsoft/DialoGPT-medium-medical (1.5GB)
2. microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract (400MB)
3. dmis-lab/biobert-base-cased-v1.1 (400MB)
"""

import asyncio
import logging
import sys
import os
import json
import time
from pathlib import Path
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.document_processing_service import DocumentProcessingService
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = get_logger(__name__)

class SmallerMedicalModelComparison:
    """Compare smaller medical models that download faster."""
    
    def __init__(self):
        self.results = {}
        
    async def test_model_configuration(self, model_name: str, provider: str, document_path: str):
        """Test a specific model configuration."""
        
        logger.info(f"🧪 Testing {model_name} ({provider}) on {Path(document_path).name}")
        
        # Temporarily update environment variables
        original_entity_model = os.environ.get('ENTITY_EXTRACTION_MODEL')
        original_entity_provider = os.environ.get('ENTITY_EXTRACTION_PROVIDER')
        original_openai_key = os.environ.get('OPENAI_API_KEY')
        
        try:
            # Set the model configuration
            os.environ['ENTITY_EXTRACTION_MODEL'] = model_name
            os.environ['ENTITY_EXTRACTION_PROVIDER'] = provider
            
            # For transformers provider, disable OpenAI fallback
            if provider.lower() == 'transformers':
                if 'OPENAI_API_KEY' in os.environ:
                    del os.environ['OPENAI_API_KEY']
                logger.info(f"🔧 Configured for {provider} with model: {model_name}")
            else:
                logger.info(f"🔧 Configured for {provider} with model: {model_name}")
            
            # Create a new document processing service
            service = DocumentProcessingService()
            
            # Record start time
            start_time = time.time()
            
            # Process the document
            result = await service.process_document(
                file_path=document_path,
                chunk_size=1200,
                overlap=0,
                extract_entities=True,
                extract_references=True,
                extract_metadata=True,
                generate_embeddings=True
            )
            
            # Record end time
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Collect results
            test_result = {
                'model_name': model_name,
                'provider': provider,
                'processing_time': processing_time,
                'success': result.get('success', False),
                'facts_count': result.get('facts_count', 0),
                'entities_count': result.get('entities_count', 0),
                'references_count': result.get('references_count', 0),
                'embeddings_count': result.get('embeddings_count', 0),
                'episode_id': result.get('episode_id'),
                'error': result.get('error') if not result.get('success', False) else None
            }
            
            logger.info(f"✅ {model_name} completed in {processing_time:.2f}s")
            logger.info(f"   Facts: {test_result['facts_count']}")
            logger.info(f"   Entities: {test_result['entities_count']}")
            logger.info(f"   References: {test_result['references_count']}")
            logger.info(f"   Embeddings: {test_result['embeddings_count']}")
            
            return test_result
            
        except Exception as e:
            logger.error(f"❌ Error testing {model_name}: {e}")
            return {
                'model_name': model_name,
                'provider': provider,
                'processing_time': 0,
                'success': False,
                'error': str(e)
            }
            
        finally:
            # Restore original environment variables
            if original_entity_model:
                os.environ['ENTITY_EXTRACTION_MODEL'] = original_entity_model
            if original_entity_provider:
                os.environ['ENTITY_EXTRACTION_PROVIDER'] = original_entity_provider
            if original_openai_key:
                os.environ['OPENAI_API_KEY'] = original_openai_key

    async def run_comparison(self, document_path: str):
        """Run comparison between models."""
        
        logger.info("🚀 Starting Smaller Medical Models comparison")
        logger.info(f"📄 Document: {Path(document_path).name}")
        
        # Test configurations - smaller models that download faster
        test_configs = [
            {
                'name': 'Gemini 1.5 Flash (Google AI)',
                'model': 'gemini-1.5-flash',
                'provider': 'google'
            },
            {
                'name': 'BioBERT Base (400MB)',
                'model': 'dmis-lab/biobert-base-cased-v1.1',
                'provider': 'transformers'
            },
            {
                'name': 'PubMedBERT Base (400MB)',
                'model': 'microsoft/BiomedNLP-PubMedBERT-base-uncased-abstract',
                'provider': 'transformers'
            }
        ]
        
        results = []
        
        for i, config in enumerate(test_configs):
            logger.info(f"\n{'='*60}")
            logger.info(f"Testing: {config['name']} ({i+1}/{len(test_configs)})")
            logger.info(f"{'='*60}")
            
            result = await self.test_model_configuration(
                config['model'],
                config['provider'],
                document_path
            )
            
            result['config_name'] = config['name']
            results.append(result)
            
            # Add a small delay between tests to allow cleanup
            logger.info(f"⏳ Waiting 5 seconds before next test...")
            await asyncio.sleep(5)
        
        # Generate comparison report
        self.generate_comparison_report(results, document_path)
        
        return results

    def generate_comparison_report(self, results: list, document_path: str):
        """Generate a detailed comparison report."""
        
        logger.info(f"\n{'='*80}")
        logger.info("📊 SMALLER MEDICAL MODELS COMPARISON REPORT")
        logger.info(f"{'='*80}")
        logger.info(f"Document: {Path(document_path).name}")
        logger.info(f"Test Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Create comparison table
        logger.info(f"\n{'Model':<45} {'Time (s)':<10} {'Facts':<8} {'Entities':<10} {'Refs':<8} {'Embeds':<8} {'Status':<10}")
        logger.info("-" * 105)
        
        for result in results:
            status = "✅ Success" if result['success'] else "❌ Failed"
            logger.info(
                f"{result['config_name']:<45} "
                f"{result['processing_time']:<10.2f} "
                f"{result.get('facts_count', 0):<8} "
                f"{result.get('entities_count', 0):<10} "
                f"{result.get('references_count', 0):<8} "
                f"{result.get('embeddings_count', 0):<8} "
                f"{status:<10}"
            )
        
        # Detailed analysis
        logger.info(f"\n📈 DETAILED ANALYSIS:")
        
        successful_results = [r for r in results if r['success']]
        
        if len(successful_results) >= 1:
            # Find best performing models
            if successful_results:
                best_entities = max(successful_results, key=lambda x: x.get('entities_count', 0))
                fastest = min(successful_results, key=lambda x: x['processing_time'])
                
                logger.info(f"\n🏆 Best Entity Extraction: {best_entities['config_name']}")
                logger.info(f"   Entities: {best_entities.get('entities_count', 0)}")
                
                logger.info(f"\n⚡ Fastest Processing: {fastest['config_name']}")
                logger.info(f"   Time: {fastest['processing_time']:.2f}s")
                
                # Compare medical models vs Google AI
                medical_results = [r for r in successful_results if 'transformers' in r.get('provider', '')]
                google_results = [r for r in successful_results if 'google' in r.get('provider', '')]
                
                if medical_results and google_results:
                    best_medical = max(medical_results, key=lambda x: x.get('entities_count', 0))
                    best_google = max(google_results, key=lambda x: x.get('entities_count', 0))
                    
                    logger.info(f"\n🔬 Medical Models vs Google AI:")
                    logger.info(f"   Best Medical Model: {best_medical['config_name']}")
                    logger.info(f"   Medical Entities: {best_medical.get('entities_count', 0)}")
                    logger.info(f"   Google AI Entities: {best_google.get('entities_count', 0)}")
                    
                    if best_medical.get('entities_count', 0) > best_google.get('entities_count', 0):
                        diff = best_medical.get('entities_count', 0) - best_google.get('entities_count', 0)
                        logger.info(f"   📈 Medical model extracted {diff} more entities!")
                    elif best_google.get('entities_count', 0) > best_medical.get('entities_count', 0):
                        diff = best_google.get('entities_count', 0) - best_medical.get('entities_count', 0)
                        logger.info(f"   📈 Google AI extracted {diff} more entities")
                    else:
                        logger.info(f"   ⚖️  Similar entity extraction performance")
        
        # Save results to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        results_file = f"smaller_medical_models_comparison_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump({
                'document': str(document_path),
                'test_date': datetime.now().isoformat(),
                'results': results
            }, f, indent=2)
        
        logger.info(f"\n💾 Results saved to: {results_file}")

async def main():
    """Main function."""
    
    # Check if transformers is installed
    try:
        import transformers
        import torch
        logger.info(f"✅ Transformers version: {transformers.__version__}")
        logger.info(f"✅ PyTorch version: {torch.__version__}")
        logger.info(f"✅ CUDA available: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"✅ CUDA device: {torch.cuda.get_device_name()}")
    except ImportError as e:
        logger.error(f"❌ Missing dependencies: {e}")
        logger.info("Please install: pip install transformers torch")
        return
    
    # Find the intestinal dysbiosis document
    document_path = None
    
    # Check uploads directory
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for file in uploads_dir.glob("*intestinal*dysbiosis*"):
            document_path = str(file)
            break
        
        if not document_path:
            for file in uploads_dir.glob("*11*intestinal*"):
                document_path = str(file)
                break
    
    if not document_path:
        logger.error("❌ Could not find '11 Intestinal Dysbiosis Notes' document")
        logger.info("Please ensure the document is in the uploads/ directory")
        return
    
    logger.info(f"📄 Found document: {document_path}")
    
    # Run the comparison
    comparison = SmallerMedicalModelComparison()
    results = await comparison.run_comparison(document_path)
    
    logger.info("\n🎉 Smaller medical models comparison completed!")

if __name__ == "__main__":
    asyncio.run(main())
