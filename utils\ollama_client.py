"""
Ollama client for generating embeddings using local models.

This module provides functions to interact with Ollama for generating embeddings
using local models like Snowflake Arctic Embed2.
"""

import json
import logging
import httpx
from typing import List, Dict, Any, Optional, Union

# Set up logger
logger = logging.getLogger(__name__)

class OllamaClient:
    """Client for interacting with Ollama API."""

    def __init__(self, base_url: str = "http://localhost:11434"):
        """
        Initialize the Ollama client.

        Args:
            base_url: Base URL for the Ollama API
        """
        self.base_url = base_url
        self.client = httpx.Client(timeout=60.0)  # Longer timeout for embedding generation
        logger.info(f"Initialized Ollama client with base URL: {base_url}")

    def generate_embeddings(self,
                           texts: Union[str, List[str]],
                           model: str = "snowflake-arctic-embed2",
                           target_dimensions: int = 1024) -> Union[List[float], List[List[float]]]:
        """
        Generate embeddings for a text or list of texts using Ollama.

        Args:
            texts: Text or list of texts to embed
            model: Embedding model to use (default: snowflake-arctic-embed2)
            target_dimensions: Target dimensions for the embedding (default: 1024)

        Returns:
            Embedding vector or list of embedding vectors
        """
        # Handle single text input
        single_input = isinstance(texts, str)
        if single_input:
            texts = [texts]

        # Ensure we have the correct model name with the latest tag if needed
        model_name = self._get_full_model_name(model)

        logger.info(f"Generating embeddings for {len(texts)} fact(s) using Ollama model: {model_name}")

        embeddings = []

        for i, text in enumerate(texts):
            try:
                # Prepare the request payload
                payload = {
                    "model": model_name,
                    "prompt": text
                }

                # Make the request to Ollama embeddings endpoint
                response = self.client.post(
                    f"{self.base_url}/api/embeddings",
                    json=payload
                )

                # Check if the request was successful
                if response.status_code == 200:
                    result = response.json()
                    embedding = result.get("embedding", [])

                    # Resize embedding to target dimensions if needed
                    if len(embedding) != target_dimensions:
                        logger.info(f"Resizing embedding from {len(embedding)} to {target_dimensions} dimensions")

                        # If the embedding is smaller than the target dimensions, pad with zeros
                        if len(embedding) < target_dimensions:
                            padding = [0.0] * (target_dimensions - len(embedding))
                            embedding = embedding + padding
                        # If the embedding is larger than the target dimensions, truncate
                        else:
                            embedding = embedding[:target_dimensions]

                    embeddings.append(embedding)
                    logger.debug(f"Successfully generated embedding for text {i+1}/{len(texts)}")
                else:
                    logger.error(f"Failed to generate embedding for text {i+1}/{len(texts)}: {response.text}")
                    # Add a placeholder embedding (zeros) to maintain the order
                    embeddings.append([0.0] * target_dimensions)

            except Exception as e:
                logger.error(f"Error generating embedding for text {i+1}/{len(texts)}: {e}")
                # Add a placeholder embedding (zeros) to maintain the order
                embeddings.append([0.0] * target_dimensions)

        logger.info(f"Generated {len(embeddings)} fact embeddings using Ollama")

        # Return single embedding if input was a single text
        if single_input:
            return embeddings[0]

        return embeddings

    def _get_full_model_name(self, model_name: str) -> str:
        """
        Get the full model name with the correct tag.

        Args:
            model_name: Base model name

        Returns:
            Full model name with tag
        """
        models = self.list_models()

        # Check for exact match first
        if any(m.get("name") == model_name for m in models):
            return model_name

        # Check for model with :latest suffix
        latest_name = f"{model_name}:latest"
        if any(m.get("name") == latest_name for m in models):
            return latest_name

        # Find any model that starts with the given name
        for m in models:
            if m.get("name", "").startswith(f"{model_name}:"):
                return m.get("name")

        # If no match found, return the original name with :latest suffix
        # This might fail, but it's a reasonable default
        return f"{model_name}:latest"

    def list_models(self) -> List[Dict[str, Any]]:
        """
        List available models in Ollama.

        Returns:
            List of model information dictionaries
        """
        try:
            response = self.client.get(f"{self.base_url}/api/tags")

            if response.status_code == 200:
                result = response.json()
                models = result.get("models", [])
                logger.info(f"Found {len(models)} models in Ollama")
                return models
            else:
                logger.error(f"Failed to list Ollama models: {response.text}")
                return []

        except Exception as e:
            logger.error(f"Error listing Ollama models: {e}")
            return []

    def is_model_available(self, model_name: str) -> bool:
        """
        Check if a specific model is available in Ollama.

        Args:
            model_name: Name of the model to check

        Returns:
            True if the model is available, False otherwise
        """
        models = self.list_models()

        # Check for exact match first
        if any(model.get("name") == model_name for model in models):
            return True

        # Check for model with :latest suffix
        if any(model.get("name") == f"{model_name}:latest" for model in models):
            return True

        # Check if the model name is a prefix of any available model
        return any(model.get("name").startswith(f"{model_name}:") for model in models)

    def is_available(self) -> bool:
        """
        Check if Ollama is available.

        Returns:
            True if Ollama is available, False otherwise
        """
        try:
            response = self.client.get(f"{self.base_url}/api/tags")
            return response.status_code == 200
        except Exception:
            return False


# Create a singleton instance
_ollama_client = None

def get_ollama_client() -> OllamaClient:
    """
    Get the Ollama client instance.

    Returns:
        OllamaClient instance
    """
    global _ollama_client

    if _ollama_client is None:
        _ollama_client = OllamaClient()

    return _ollama_client
