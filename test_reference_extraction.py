#!/usr/bin/env python3
"""
Test reference extraction with different methods to find the best approach.
"""

import asyncio
import re
from pathlib import Path
from typing import List, Dict, Any

# Test with PyMuPDF for better text extraction
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
    print("✅ PyMuPDF available")
except ImportError:
    PYMUPDF_AVAILABLE = False
    print("❌ PyMuPDF not available")

# Test with PyPDF2 (current method)
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
    print("✅ PyPDF2 available")
except ImportError:
    PYPDF2_AVAILABLE = False
    print("❌ PyPDF2 not available")

def extract_text_with_pymupdf(pdf_path: str) -> str:
    """Extract text using PyMuPDF (better text extraction)."""
    if not PYMUPDF_AVAILABLE:
        return ""
    
    try:
        doc = fitz.open(pdf_path)
        text = ""
        
        for page_num in range(len(doc)):
            page = doc.load_page(page_num)
            text += page.get_text()
            text += "\n\n"  # Add page breaks
        
        doc.close()
        return text
    except Exception as e:
        print(f"❌ PyMuPDF extraction failed: {e}")
        return ""

def extract_text_with_pypdf2(pdf_path: str) -> str:
    """Extract text using PyPDF2 (current method)."""
    if not PYPDF2_AVAILABLE:
        return ""
    
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            text = ""
            
            for page in reader.pages:
                text += page.extract_text()
                text += "\n\n"  # Add page breaks
        
        return text
    except Exception as e:
        print(f"❌ PyPDF2 extraction failed: {e}")
        return ""

def find_reference_sections(text: str) -> List[str]:
    """Find reference sections in text."""
    sections = []
    
    # Enhanced reference section patterns
    ref_headers = [
        r'(?:^|\n)\s*#{1,6}\s*\*?\*?\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited)\s*\*?\*?\s*(?:\n|$)',
        r'(?:^|\n)\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited)\s*(?:\n|$)',
        r'(?:^|\n)\s*\d+\.\s*(?:REFERENCES?|Bibliography)\s*(?:\n|$)',
        r'(?:^|\n)\s*(?:References?|Bibliography)\s*(?:\n|$)',
        r'(?:^|\n)\s*\*\*\s*(?:REFERENCES?|Bibliography|Citations?)\s*\*\*\s*(?:\n|$)',
    ]
    
    for header_pattern in ref_headers:
        matches = list(re.finditer(header_pattern, text, re.IGNORECASE | re.MULTILINE))
        for match in matches:
            start = match.end()
            # Look for end of references (next major section)
            end_patterns = [
                r'(?:\n|^)\s*(?:Appendix|Acknowledgments?|Author\s+Information|Supplementary|Figure|Table)\s*(?:\n|$)',
                r'(?:\n|^)\s*\d+\.\s*(?:Appendix|Acknowledgments?)\s*(?:\n|$)'
            ]
            
            end = len(text)
            for end_pattern in end_patterns:
                end_match = re.search(end_pattern, text[start:], re.IGNORECASE | re.MULTILINE)
                if end_match:
                    end = start + end_match.start()
                    break
            
            section = text[start:end].strip()
            if len(section) > 50:
                sections.append(section)
    
    return sections

def extract_numbered_references(text: str) -> List[str]:
    """Extract numbered references using multiple patterns."""
    references = []
    
    # Multiple numbered reference patterns
    patterns = [
        # Standard numbered: "1. Author..."
        r'(?:^|\n)\s*(\d+)\.\s+([A-Z][^.]*\.(?:[^.]*\.)*)',
        
        # Spaced numbered: "1 Author..." (common in some formats)
        r'(?:^|\n)\s*(\d+)\s+([A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z][a-z]+)*[^.]*\.(?:[^.]*\.)*)',
        
        # Bracketed: "[1] Author..."
        r'(?:^|\n)\s*\[(\d+)\]\s+([A-Z][^.]*\.(?:[^.]*\.)*)',
        
        # Dash bracketed: "- [1] Author..."
        r'(?:^|\n)\s*-\s*\[(\d+)\]\s+([A-Z][^.]*\.(?:[^.]*\.)*)',
    ]
    
    for pattern in patterns:
        try:
            matches = re.finditer(pattern, text, re.MULTILINE | re.DOTALL)
            pattern_refs = []
            
            for match in matches:
                if len(match.groups()) >= 2:
                    ref_num = match.group(1)
                    ref_text = match.group(2).strip()
                    
                    # Basic validation
                    if len(ref_text) > 20 and '.' in ref_text:
                        pattern_refs.append(f"{ref_num}. {ref_text}")
            
            if len(pattern_refs) > len(references):
                references = pattern_refs
                print(f"✅ Pattern found {len(pattern_refs)} references")
        
        except re.error as e:
            print(f"❌ Regex error: {e}")
            continue
    
    return references

async def test_reference_extraction():
    """Test reference extraction on uploaded PDFs."""
    uploads_dir = Path("uploads")
    
    if not uploads_dir.exists():
        print("❌ No uploads directory found")
        return
    
    pdf_files = list(uploads_dir.glob("*.pdf"))
    if not pdf_files:
        print("❌ No PDF files found in uploads")
        return
    
    # Test with the first PDF
    test_file = pdf_files[0]
    print(f"\n🔍 Testing reference extraction on: {test_file.name}")
    
    # Method 1: PyMuPDF
    if PYMUPDF_AVAILABLE:
        print("\n📖 Method 1: PyMuPDF text extraction")
        pymupdf_text = extract_text_with_pymupdf(str(test_file))
        print(f"  Text length: {len(pymupdf_text):,} characters")
        
        pymupdf_sections = find_reference_sections(pymupdf_text)
        print(f"  Reference sections found: {len(pymupdf_sections)}")
        
        if pymupdf_sections:
            pymupdf_refs = extract_numbered_references(pymupdf_sections[0])
            print(f"  References extracted: {len(pymupdf_refs)}")
            
            # Show first few references
            for i, ref in enumerate(pymupdf_refs[:3]):
                print(f"    {i+1}. {ref[:100]}...")
    
    # Method 2: PyPDF2
    if PYPDF2_AVAILABLE:
        print("\n📖 Method 2: PyPDF2 text extraction")
        pypdf2_text = extract_text_with_pypdf2(str(test_file))
        print(f"  Text length: {len(pypdf2_text):,} characters")
        
        pypdf2_sections = find_reference_sections(pypdf2_text)
        print(f"  Reference sections found: {len(pypdf2_sections)}")
        
        if pypdf2_sections:
            pypdf2_refs = extract_numbered_references(pypdf2_sections[0])
            print(f"  References extracted: {len(pypdf2_refs)}")
            
            # Show first few references
            for i, ref in enumerate(pypdf2_refs[:3]):
                print(f"    {i+1}. {ref[:100]}...")
    
    # Method 3: Test current system
    print("\n📖 Method 3: Current system test")
    try:
        from services.reference_processor import ReferenceProcessor
        processor = ReferenceProcessor()
        
        result = await processor.extract_references_from_document(str(test_file))
        print(f"  Current system extracted: {result.get('total_reference_count', 0)} references")
        
    except Exception as e:
        print(f"  ❌ Current system failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_reference_extraction())
