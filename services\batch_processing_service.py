"""
Batch processing service optimized for fast document processing.
Uses traditional processors (Mistral OCR + PyPDF2) instead of Docling for better performance.
"""

import asyncio
from typing import Dict, Any, Union, List
from pathlib import Path
import uuid
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor

from services.batch_document_processor import BatchDocumentProcessor
from services.entity_processor import EntityProcessor
from services.reference_processor import ReferenceProcessor
from services.embedding_processor import EmbeddingProcessor
from database.falkordb_adapter import FalkorDBAdapter
from utils.logging_utils import get_logger
from utils.progress_tracker import ProgressTracker

logger = get_logger(__name__)

class BatchProcessingService:
    """
    Service for fast batch processing of documents using traditional processors.
    """
    
    def __init__(self):
        """Initialize the batch processing service."""
        self.document_processor = BatchDocumentProcessor()
        self.entity_processor = EntityProcessor()
        self.reference_processor = ReferenceProcessor()
        self.embedding_processor = EmbeddingProcessor()
        self.db_adapter = FalkorDBAdapter()
        
        # Processing state
        self.processing_queue = asyncio.Queue()
        self.is_processing = False
        self.max_parallel_processes = 4
        self.semaphore = asyncio.Semaphore(self.max_parallel_processes)
        self.current_document_progress = None
        
        logger.info("✅ Batch processing service initialized with fast traditional processors")
    
    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_entities: bool = True,
        extract_references: bool = True,
        extract_metadata: bool = True,
        generate_embeddings: bool = True
    ) -> Dict[str, Any]:
        """
        Process a single document using fast traditional methods.
        
        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks in characters
            overlap: Overlap between chunks in characters
            extract_entities: Whether to extract entities
            extract_references: Whether to extract references
            extract_metadata: Whether to extract metadata
            generate_embeddings: Whether to generate embeddings for facts
            
        Returns:
            Processing result dictionary
        """
        file_path = Path(file_path)
        
        # Initialize progress tracking
        document_id = str(file_path.stem)
        filename = file_path.name
        progress_tracker = ProgressTracker(document_id, filename)
        
        # Update current document progress
        self.current_document_progress = progress_tracker.get_progress_data()
        
        try:
            # Step 1: Extract text using fast traditional processor
            progress_tracker.update_progress(
                step=1,
                step_name="Extracting text with fast processor",
                progress_percentage=10,
                status="processing"
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            
            logger.info(f"🚀 Fast processing document: {filename}")
            document_result = await self.document_processor.process_document(
                file_path=file_path,
                chunk_size=chunk_size,
                overlap=overlap,
                extract_metadata=extract_metadata
            )
            
            if not document_result.get("success", False):
                logger.error(f"❌ Fast processing failed: {filename}")
                progress_tracker.fail(document_result.get("error", "Unknown error"))
                self.current_document_progress = progress_tracker.get_progress_data()
                return document_result
            
            text = document_result.get("text", "")
            metadata = document_result.get("metadata", {})
            
            progress_tracker.update_progress(
                step=1,
                step_name="Text extraction completed",
                progress_percentage=20,
                status="processing",
                details={"characters_extracted": len(text)}
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            
            logger.info(f"✅ Fast text extraction completed: {filename} ({len(text)} characters)")
            
            # Step 2: Create episode and store in knowledge graph
            progress_tracker.update_progress(
                step=2,
                step_name="Creating knowledge graph episode",
                progress_percentage=30,
                status="processing"
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            
            # Generate episode ID
            episode_id = str(uuid.uuid4())
            
            # Create chunks for processing
            chunks = self._create_chunks(text, chunk_size, overlap)
            
            # Store document in knowledge graph using correct database service methods
            from database.database_service import create_episode_node, create_fact_node

            # Create episode node
            episode_properties = {
                "uuid": episode_id,
                "file_path": str(file_path),
                "processed_at": datetime.now().isoformat(),
                "chunks_count": len(chunks),
                "processor_type": "batch_traditional"
            }
            episode_properties.update(metadata)

            created_episode_id = await create_episode_node(filename, episode_properties)
            if not created_episode_id:
                raise Exception("Failed to create episode node")

            # Create fact nodes for each chunk
            for i, chunk in enumerate(chunks):
                fact_properties = {
                    "chunk_index": i,
                    "chunk_size": len(chunk),
                    "processor_type": "batch_traditional"
                }
                await create_fact_node(chunk, episode_id, fact_properties)
            
            progress_tracker.update_progress(
                step=2,
                step_name="Document stored in knowledge graph",
                progress_percentage=40,
                status="processing",
                details={"episode_id": episode_id, "chunks": len(chunks)}
            )
            self.current_document_progress = progress_tracker.get_progress_data()
            
            logger.info(f"✅ Document stored in knowledge graph: {episode_id}")
            
            # Prepare result
            result = {
                "success": True,
                "episode_id": episode_id,
                "filename": filename,
                "text": text,
                "metadata": metadata,
                "chunks": len(chunks),
                "characters_extracted": len(text),
                "processor_type": "batch_traditional"
            }
            
            # Step 3: Extract entities if requested
            if extract_entities:
                progress_tracker.update_progress(
                    step=3,
                    step_name="Extracting entities",
                    progress_percentage=50,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                entities_result = await self.entity_processor.extract_entities_from_text(
                    text=text,
                    episode_id=episode_id
                )
                
                result["entities_extracted"] = entities_result.get("entities_extracted", 0)
                
                progress_tracker.update_progress(
                    step=3,
                    step_name="Entity extraction completed",
                    progress_percentage=60,
                    status="processing",
                    details={"entities": result["entities_extracted"]}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                logger.info(f"✅ Entities extracted: {result['entities_extracted']}")
            
            # Step 4: Extract references if requested
            if extract_references:
                progress_tracker.update_progress(
                    step=4,
                    step_name="Extracting references",
                    progress_percentage=70,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                references_result = await self.reference_processor.extract_references_from_document(
                    str(file_path)
                )
                
                result["references_extracted"] = references_result.get("total_reference_count", 0)
                result["references_file"] = references_result.get("csv_file_path")
                
                progress_tracker.update_progress(
                    step=4,
                    step_name="Reference extraction completed",
                    progress_percentage=80,
                    status="processing",
                    details={"references": result["references_extracted"]}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                logger.info(f"✅ References extracted: {result['references_extracted']}")
            
            # Step 5: Generate embeddings if requested
            if generate_embeddings:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Generating embeddings",
                    progress_percentage=90,
                    status="processing"
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                embeddings_result = await self.embedding_processor.generate_embeddings_for_episode(
                    episode_id=episode_id
                )
                
                result["embeddings_generated"] = embeddings_result.get("embeddings_generated", 0)
                
                progress_tracker.update_progress(
                    step=5,
                    step_name="Embedding generation completed",
                    progress_percentage=95,
                    status="processing",
                    details={"embeddings": result["embeddings_generated"]}
                )
                self.current_document_progress = progress_tracker.get_progress_data()
                
                logger.info(f"✅ Embeddings generated: {result['embeddings_generated']}")
            
            # Complete processing
            progress_tracker.complete(result)
            self.current_document_progress = progress_tracker.get_progress_data()
            
            logger.info(f"🎉 Fast batch processing completed successfully: {filename}")
            return result
            
        except Exception as e:
            error_msg = f"Error in fast batch processing {filename}: {str(e)}"
            logger.error(error_msg)
            progress_tracker.fail(error_msg)
            self.current_document_progress = progress_tracker.get_progress_data()
            return {
                "success": False,
                "error": error_msg
            }
    
    def _create_chunks(self, text: str, chunk_size: int, overlap: int) -> List[str]:
        """Create text chunks for processing."""
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            chunk = text[start:end]
            chunks.append(chunk)
            
            if end >= len(text):
                break
            
            start = end - overlap
        
        return chunks
    
    def get_current_progress(self) -> Dict[str, Any]:
        """Get current document processing progress."""
        return self.current_document_progress or {}

# Global instance
_batch_processing_service = None

async def get_batch_processing_service() -> BatchProcessingService:
    """Get the global batch processing service instance."""
    global _batch_processing_service
    if _batch_processing_service is None:
        _batch_processing_service = BatchProcessingService()
    return _batch_processing_service
