"""
PDF document processor using Mistral OCR with PyPDF2 fallback.
"""

import asyncio
from typing import Dict, Any
from pathlib import Path
import logging

from .base_processor import BaseDocumentProcessor
from utils.mistral_ocr import MistralOCRProcessor

logger = logging.getLogger(__name__)

class PDFProcessor(BaseDocumentProcessor):
    """
    PDF document processor with Mistral OCR primary and PyPDF2 fallback.
    """
    
    def __init__(self):
        """Initialize the PDF processor."""
        super().__init__()
        self.supported_extensions = {'.pdf'}
        self.mistral_ocr = MistralOCRProcessor()
    
    def supports_file(self, file_path: str) -> bool:
        """Check if this processor supports PDF files."""
        return self.get_file_extension(file_path) in self.supported_extensions
    
    async def process_document(self, file_path: str) -> Dict[str, Any]:
        """
        Process a PDF document and extract text.
        
        Args:
            file_path: Path to the PDF file
            
        Returns:
            Processing result dictionary
        """
        self.log_processing_start(file_path)
        
        # Validate file
        validation = self.validate_file(file_path)
        if not validation["valid"]:
            return {
                "success": False,
                "error": validation["error"]
            }
        
        try:
            # Try Mistral OCR first
            text = await self._extract_with_mistral_ocr(file_path)
            
            if not text:
                # Fallback to PyPDF2
                text = await self._extract_with_pypdf2(file_path)
            
            if not text:
                return {
                    "success": False,
                    "error": "No text could be extracted from PDF"
                }
            
            # Extract metadata
            metadata = await self.extract_metadata(file_path)
            metadata.update(await self._extract_pdf_metadata(file_path))
            
            self.log_processing_success(file_path, len(text))
            
            return {
                "success": True,
                "text": text,
                "metadata": metadata,
                "extraction_method": "mistral_ocr" if await self._has_mistral_api_key() else "pypdf2"
            }
            
        except Exception as e:
            error_msg = f"Error processing PDF: {str(e)}"
            self.log_processing_error(file_path, error_msg)
            return {
                "success": False,
                "error": error_msg
            }
    
    async def _extract_with_mistral_ocr(self, file_path: str) -> str:
        """Extract text using Mistral OCR."""
        try:
            if not await self._has_mistral_api_key():
                logger.info("Mistral API key not available, skipping Mistral OCR")
                return ""
            
            logger.info(f"Extracting text with Mistral OCR: {Path(file_path).name}")
            result = await self.mistral_ocr.process_pdf(file_path)
            
            if result.get("success"):
                text = result.get("text", "")
                logger.info(f"Mistral OCR extracted {len(text)} characters")
                return text
            else:
                logger.warning(f"Mistral OCR failed: {result.get('error')}")
                return ""
                
        except Exception as e:
            logger.warning(f"Mistral OCR error: {e}")
            return ""
    
    async def _extract_with_pypdf2(self, file_path: str) -> str:
        """Extract text using PyPDF2 as fallback."""
        try:
            import PyPDF2
            
            logger.info(f"Extracting text with PyPDF2: {Path(file_path).name}")
            
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num, page in enumerate(pdf_reader.pages):
                    try:
                        page_text = page.extract_text()
                        if page_text:
                            text += page_text + "\n"
                    except Exception as e:
                        logger.warning(f"Error extracting page {page_num}: {e}")
                        continue
            
            logger.info(f"PyPDF2 extracted {len(text)} characters")
            return text.strip()
            
        except ImportError:
            logger.error("PyPDF2 not installed. Install with: pip install PyPDF2")
            return ""
        except Exception as e:
            logger.error(f"PyPDF2 extraction error: {e}")
            return ""
    
    async def _extract_pdf_metadata(self, file_path: str) -> Dict[str, Any]:
        """Extract PDF-specific metadata."""
        metadata = {}
        
        try:
            import PyPDF2
            
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Basic PDF info
                metadata["page_count"] = len(pdf_reader.pages)
                
                # Document info
                if pdf_reader.metadata:
                    doc_info = pdf_reader.metadata
                    metadata.update({
                        "title": doc_info.get("/Title", ""),
                        "author": doc_info.get("/Author", ""),
                        "subject": doc_info.get("/Subject", ""),
                        "creator": doc_info.get("/Creator", ""),
                        "producer": doc_info.get("/Producer", ""),
                        "creation_date": str(doc_info.get("/CreationDate", "")),
                        "modification_date": str(doc_info.get("/ModDate", ""))
                    })
                
        except Exception as e:
            logger.warning(f"Could not extract PDF metadata: {e}")
        
        return metadata
    
    async def _has_mistral_api_key(self) -> bool:
        """Check if Mistral API key is available."""
        return self.mistral_ocr.api_key is not None
