#!/usr/bin/env python3
"""
Test the batch processing service to ensure it works correctly.
"""

import asyncio
from pathlib import Path
from services.batch_processing_service import get_batch_processing_service
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def test_batch_processing():
    """Test the batch processing service."""
    
    try:
        # Get the batch processing service
        logger.info("Getting batch processing service...")
        service = await get_batch_processing_service()
        logger.info("✅ Batch processing service initialized")
        
        # Check if there are any uploaded files to test with
        uploads_dir = Path("uploads")
        if not uploads_dir.exists():
            logger.info("No uploads directory found")
            return
        
        # Find a PDF file to test with
        pdf_files = list(uploads_dir.glob("*.pdf"))
        if not pdf_files:
            logger.info("No PDF files found in uploads directory")
            return
        
        # Test with the first PDF file
        test_file = pdf_files[0]
        logger.info(f"Testing batch processing with: {test_file.name}")
        
        # Process the document
        result = await service.process_document(
            file_path=test_file,
            chunk_size=1200,
            overlap=0,
            extract_entities=False,  # Skip entities for quick test
            extract_references=False,  # Skip references for quick test
            extract_metadata=True,
            generate_embeddings=False  # Skip embeddings for quick test
        )
        
        if result.get("success", False):
            logger.info("🎉 Batch processing test SUCCESSFUL!")
            logger.info(f"  Episode ID: {result.get('episode_id')}")
            logger.info(f"  Characters extracted: {result.get('characters_extracted')}")
            logger.info(f"  Chunks created: {result.get('chunks')}")
            logger.info(f"  Processor type: {result.get('processor_type')}")
        else:
            logger.error(f"❌ Batch processing test FAILED: {result.get('error')}")
            
    except Exception as e:
        logger.error(f"❌ Error testing batch processing: {e}")

if __name__ == "__main__":
    asyncio.run(test_batch_processing())
