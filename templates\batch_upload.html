{% extends "base_modern.html" %}

{% block title %}Batch Upload - Graphiti{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed var(--bs-primary);
        border-radius: 12px;
        padding: 40px;
        text-align: center;
        margin-bottom: 24px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
        position: relative;
        overflow: hidden;
    }

    .upload-area::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
        transform: translateX(-100%);
        transition: transform 0.6s;
    }

    .upload-area:hover::before {
        transform: translateX(100%);
    }

    .upload-area.drag-over {
        border-color: var(--bs-success);
        background: linear-gradient(135deg, rgba(17, 153, 142, 0.1) 0%, rgba(56, 239, 125, 0.1) 100%);
        transform: scale(1.02);
    }

    .upload-icon {
        font-size: 3rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }

    .file-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border: 1px solid #e9ecef;
        background: white;
        border-radius: 8px;
        margin-bottom: 12px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }

    .file-item:hover {
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        transform: translateY(-1px);
    }

    .file-name {
        flex-grow: 1;
        margin-right: 15px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-weight: 500;
    }

    .file-size {
        margin-right: 15px;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .settings-section {
        margin-bottom: 24px;
    }

    .settings-section h6 {
        margin-bottom: 16px;
        color: #495057;
        font-weight: 600;
    }

    /* Progress styles */
    .progress {
        height: 12px;
        margin-bottom: 12px;
        border-radius: 6px;
        background-color: #f8f9fa;
    }

    .progress-bar {
        border-radius: 6px;
    }

    .status-text {
        font-size: 0.9rem;
        font-weight: 500;
    }

    #file-progress-list .list-group-item {
        padding: 16px;
        transition: all 0.3s ease;
        border: none;
        border-bottom: 1px solid #f1f3f4;
    }

    #file-progress-list .list-group-item:hover {
        background-color: #f8f9fa;
    }

    .stats-card {
        transition: all 0.3s ease;
    }

    .stats-card:hover {
        transform: translateY(-2px);
    }

    .stats-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block content %}
<!-- Page Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">Batch Document Upload</h1>
        <p class="text-muted">Upload and process multiple documents at once</p>
    </div>
    <div>
        <button class="btn btn-outline-secondary" onclick="clearAllFiles()">
            <i class="bi bi-trash"></i> Clear All
        </button>
        <button class="btn btn-outline-secondary ms-2" onclick="location.reload()">
            <i class="bi bi-arrow-clockwise"></i> Refresh
        </button>
    </div>
</div>

<!-- Stats Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card gradient-card stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="files-selected">0</div>
                <div class="stats-label">
                    <i class="bi bi-files"></i> Files Selected
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-success stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="files-processed">0</div>
                <div class="stats-label">
                    <i class="bi bi-check-circle"></i> Processed
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-warning stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="total-size">0 MB</div>
                <div class="stats-label">
                    <i class="bi bi-hdd"></i> Total Size
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card gradient-card-info stats-card">
            <div class="card-body text-center">
                <div class="stats-number" id="processing-time">0s</div>
                <div class="stats-label">
                    <i class="bi bi-clock"></i> Processing Time
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-cloud-upload"></i> File Upload
                </h5>
            </div>
            <div class="card-body">
                <form id="upload-form">
                    <div class="upload-area">
                        <div class="upload-icon">
                            <i class="bi bi-cloud-upload"></i>
                        </div>
                        <h5 class="mb-3">Drag and drop files or folders here</h5>
                        <div class="d-flex gap-2 justify-content-center mb-3">
                            <button type="button" class="btn btn-primary" id="select-files-button">
                                <i class="bi bi-file-earmark"></i> Select Files
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="select-folder-button">
                                <i class="bi bi-folder"></i> Select Folder
                            </button>
                        </div>
                        <p class="text-muted small">Supported formats: PDF, TXT, DOC, DOCX, OneNote (.one), and more</p>
                        <input type="file" id="file-input" class="form-control d-none" multiple accept=".pdf,.txt,.md,.rtf,.doc,.docx,.odt,.html,.htm,.xml,.csv,.xls,.xlsx,.ppt,.pptx,.epub,.one">
                        <input type="file" id="folder-input" class="form-control d-none" webkitdirectory directory multiple>
                    </div>

                    <div id="file-list" class="mb-4">
                        <!-- Files will be listed here -->
                    </div>

                    <div class="d-flex gap-2">
                        <button type="submit" id="upload-button" class="btn btn-primary" disabled>
                            <i class="bi bi-upload"></i> Upload Files
                        </button>
                        <button type="button" id="clear-files-button" class="btn btn-outline-secondary" onclick="clearAllFiles()">
                            <i class="bi bi-x-circle"></i> Clear Files
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Progress Section -->
        <div id="progress-container" class="card d-none">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Processing Status
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span id="progress-text" class="status-text">Uploading files...</span>
                        <span id="progress-percentage" class="badge bg-primary">0%</span>
                    </div>
                    <div class="progress">
                        <div id="progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                </div>

                <div id="file-progress-list" class="list-group list-group-flush">
                    <!-- Individual file progress will be displayed here -->
                </div>

                <div class="mt-3">
                    <button type="button" id="stop-processing-button" class="btn btn-outline-danger btn-sm">
                        <i class="bi bi-stop-circle"></i> Stop Processing
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <!-- Processing Settings -->
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-gear"></i> Processing Settings
                </h5>
            </div>
            <div class="card-body">
                <div class="settings-section">
                    <h6><i class="bi bi-scissors"></i> Chunking Settings</h6>
                    <div class="mb-3">
                        <label for="chunk-size" class="form-label">Chunk Size (characters)</label>
                        <input type="number" id="chunk-size" class="form-control" value="1200" min="100" max="10000">
                        <div class="form-text">Size of text chunks (100-10000)</div>
                    </div>
                    <div class="mb-3">
                        <label for="overlap" class="form-label">Overlap (characters)</label>
                        <input type="number" id="overlap" class="form-control" value="0" min="0" max="500">
                        <div class="form-text">Overlap between chunks (0-500)</div>
                    </div>
                </div>

                <div class="settings-section">
                    <h6><i class="bi bi-extract"></i> Extraction Settings</h6>
                    <div class="form-check mb-3">
                        <input type="checkbox" id="extract-entities" class="form-check-input" checked>
                        <label for="extract-entities" class="form-check-label">
                            <i class="bi bi-diagram-3"></i> Extract Entities
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" id="extract-references" class="form-check-input" checked>
                        <label for="extract-references" class="form-check-label">
                            <i class="bi bi-bookmark"></i> Extract References
                        </label>
                    </div>
                    <div class="form-check mb-3">
                        <input type="checkbox" id="extract-metadata" class="form-check-input" checked>
                        <label for="extract-metadata" class="form-check-label">
                            <i class="bi bi-info-circle"></i> Extract Metadata
                        </label>
                    </div>
                </div>

                <div class="settings-section">
                    <h6><i class="bi bi-cpu"></i> Parallel Processing</h6>
                    <div class="mb-3">
                        <label for="max-parallel-processes" class="form-label">Max Parallel Processes</label>
                        <input type="number" id="max-parallel-processes" class="form-control" value="4" min="1" max="10">
                        <div class="form-text">Maximum number of documents to process in parallel (1-10)</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="bi bi-lightning"></i> Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary btn-sm" onclick="window.location.href='/documents'">
                        <i class="bi bi-file-earmark-text"></i> View Documents
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="window.location.href='/entities'">
                        <i class="bi bi-diagram-3"></i> View Entities
                    </button>
                    <button class="btn btn-outline-info btn-sm" onclick="window.location.href='/references'">
                        <i class="bi bi-bookmark"></i> View References
                    </button>
                    <button class="btn btn-outline-warning btn-sm" onclick="window.location.href='/knowledge-graph'">
                        <i class="bi bi-share"></i> Knowledge Graph
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log("Batch upload page loaded");

    // Initialize stats
    let selectedFiles = [];
    let processedFiles = 0;
    let totalSize = 0;
    let startTime = null;

    // Update stats display
    function updateStats() {
        document.getElementById('files-selected').textContent = selectedFiles.length;
        document.getElementById('files-processed').textContent = processedFiles;
        document.getElementById('total-size').textContent = (totalSize / (1024 * 1024)).toFixed(1) + ' MB';

        if (startTime) {
            const elapsed = Math.floor((Date.now() - startTime) / 1000);
            document.getElementById('processing-time').textContent = elapsed + 's';
        }
    }

    // Clear all files function
    function clearAllFiles() {
        selectedFiles = [];
        processedFiles = 0;
        totalSize = 0;
        startTime = null;
        updateStats();

        // Clear file list
        const fileList = document.getElementById('file-list');
        if (fileList) {
            fileList.innerHTML = '';
        }

        // Hide progress container
        const progressContainer = document.getElementById('progress-container');
        if (progressContainer) {
            progressContainer.classList.add('d-none');
        }

        // Reset upload button
        const uploadButton = document.getElementById('upload-button');
        if (uploadButton) {
            uploadButton.disabled = true;
        }

        showAlert('All files cleared', 'info');
    }

    // Show alert function
    function showAlert(message, type = 'info') {
        const alertContainer = document.getElementById('alert-container');
        if (alertContainer) {
            const alert = document.createElement('div');
            alert.className = `alert alert-${type} alert-dismissible fade show`;
            alert.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            alertContainer.appendChild(alert);

            // Auto-dismiss after 5 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 5000);
        }
    }

    // Initialize on page load
    window.addEventListener('load', function() {
        console.log("Window loaded");
        updateStats();
    });
</script>
<script src="/static/js/batch_upload.js?v=3"></script>
{% endblock %}
