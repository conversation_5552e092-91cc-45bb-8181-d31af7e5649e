#!/usr/bin/env python3
"""
Debug script to test entity merging specifically.
"""

import asyncio
import logging
import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from entity_deduplication import EntityDeduplicator
from utils.logging_utils import get_logger

# Set up logging
logging.basicConfig(level=logging.DEBUG)
logger = get_logger(__name__)

async def test_merge_debug():
    """Test entity merging with debug logging."""
    
    logger.info("🔍 Testing entity merge with debug logging...")
    
    try:
        # Initialize deduplicator
        deduplicator = EntityDeduplicator()
        
        # Test with a very small batch to see what happens
        logger.info("Running deduplication on 10 entities...")
        result = await deduplicator.deduplicate_entities(
            merge=True,
            limit=10  # Very small test
        )
        
        # Log detailed results
        logger.info("📊 Merge Debug Results:")
        logger.info(f"  Total entities processed: {result.total_entities}")
        logger.info(f"  Duplicate groups found: {result.duplicate_groups}")
        logger.info(f"  Total duplicate matches: {result.total_duplicates}")
        logger.info(f"  Entities merged: {result.merged_entities}")
        
        if result.total_duplicates > 0:
            logger.info("🔍 Analyzing matches:")
            for i, match in enumerate(result.entity_matches[:3]):
                logger.info(f"  Match {i+1}: '{match.source_name}' -> '{match.target_name}'")
                logger.info(f"    Score: {match.similarity_score:.3f}, Type: {match.match_type}")
                logger.info(f"    Source UUID: {match.source_uuid}")
                logger.info(f"    Target UUID: {match.target_uuid}")
                logger.info(f"    Merged: {match.merged}")
        
        return result
        
    except Exception as e:
        logger.error(f"❌ Error during merge debug test: {e}", exc_info=True)
        return None

async def main():
    """Main test function."""
    
    logger.info("🚀 Starting merge debug test...")
    
    # Test merge debugging
    await test_merge_debug()
    
    logger.info("✅ Debug test completed!")

if __name__ == "__main__":
    asyncio.run(main())
