#!/usr/bin/env python3
"""
Cleanup script for stuck document processing operations.
"""

import asyncio
import requests
import json
from pathlib import Path
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def cleanup_stuck_operations():
    """Clean up stuck operations that reference non-existent files."""
    
    base_url = "http://localhost:9753"
    
    try:
        # Get active persistent operations
        response = requests.get(f"{base_url}/ws/operations/active-persistent")
        if response.status_code == 200:
            operations = response.json()
            logger.info(f"Found {len(operations)} active persistent operations")
            
            for operation in operations:
                operation_id = operation.get('operation_id')
                filename = operation.get('filename', 'unknown')
                
                logger.info(f"Checking operation {operation_id}: {filename}")
                
                # Check if this operation references a file that doesn't exist
                if 'Biopractia' in filename or not Path(f"uploads").glob(f"*{filename}*"):
                    logger.warning(f"Operation {operation_id} references missing file: {filename}")
                    
                    # Clean up the stuck operation
                    cleanup_response = requests.post(
                        f"{base_url}/ws/operation/{operation_id}/cleanup",
                        json={"reason": "File not found - cleaning up stuck operation"}
                    )
                    
                    if cleanup_response.status_code == 200:
                        logger.info(f"✅ Successfully cleaned up operation {operation_id}")
                    else:
                        logger.error(f"❌ Failed to clean up operation {operation_id}: {cleanup_response.text}")
        else:
            logger.error(f"Failed to get active operations: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error during cleanup: {e}")

    try:
        # Also check recent operations for any that might be stuck
        response = requests.get(f"{base_url}/api/enhanced/recent-operations")
        if response.status_code == 200:
            recent_ops = response.json()
            logger.info(f"Found {len(recent_ops)} recent operations")
            
            for op in recent_ops:
                if op.get('status') in ['processing', 'pending'] and 'Biopractia' in op.get('filename', ''):
                    operation_id = op.get('operation_id')
                    logger.warning(f"Found stuck recent operation: {operation_id}")
                    
                    # Try to clean it up
                    cleanup_response = requests.post(
                        f"{base_url}/ws/operation/{operation_id}/cleanup",
                        json={"reason": "Stuck operation with missing files"}
                    )
                    
                    if cleanup_response.status_code == 200:
                        logger.info(f"✅ Cleaned up stuck recent operation {operation_id}")
                    else:
                        logger.warning(f"Could not clean up recent operation {operation_id}")
                        
    except Exception as e:
        logger.error(f"Error checking recent operations: {e}")

if __name__ == "__main__":
    asyncio.run(cleanup_stuck_operations())
