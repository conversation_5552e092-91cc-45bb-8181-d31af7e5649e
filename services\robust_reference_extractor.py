#!/usr/bin/env python3
"""
Robust Reference Extractor - A comprehensive solution for extracting references from academic documents.

This extractor uses multiple strategies and text extraction methods to maximize reference detection.
"""

import re
import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import uuid

# Try to import PyMuPDF for better text extraction
try:
    import fitz  # PyMuPDF
    PYMUPDF_AVAILABLE = True
except ImportError:
    PYMUPDF_AVAILABLE = False

# Fallback to PyPDF2
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False

logger = logging.getLogger(__name__)

class RobustReferenceExtractor:
    """
    A robust reference extractor that uses multiple strategies to find references.
    """
    
    def __init__(self):
        """Initialize the robust reference extractor."""
        self.reference_patterns = self._build_comprehensive_patterns()
        self.section_headers = self._build_section_headers()
        
    def _build_comprehensive_patterns(self) -> Dict[str, List[str]]:
        """Build comprehensive regex patterns for different reference formats."""
        return {
            # Numbered references with period: "1. Author..."
            'numbered_period': [
                r'(?:^|\n)\s*(\d+)\.\s+([A-Z][^.]*\.(?:[^.]*\.)*[^.]*\.)',
                r'(?:^|\n)\s*(\d+)\.\s+([A-Z].*?)(?=\n\s*\d+\.|$)',
                r'(\d+)\.\s+([A-Z][a-zA-Z\s,]+\(\d{4}\)[^.]*\.(?:[^.]*\.)*)',
            ],
            
            # Numbered references with space: "1 Author..." (common format)
            'numbered_space': [
                r'(?:^|\n)\s*(\d+)\s+([A-Z][a-z]+(?:\s+[A-Z]{1,3})?(?:,\s*[A-Z][a-z]+)*[^.]*\.(?:[^.]*\.)*)',
                r'(?:^|\n)\s*(\d+)\s+([A-Z].*?)(?=\n\s*\d+\s+|$)',
                r'(\d+)\s+([A-Z][a-zA-Z\s,]+\(\d{4}\)[^.]*\.(?:[^.]*\.)*)',
            ],
            
            # Bracketed references: "[1] Author..."
            'bracketed': [
                r'(?:^|\n)\s*\[(\d+)\]\s+([A-Z][^.]*\.(?:[^.]*\.)*)',
                r'(?:^|\n)\s*\[(\d+)\]\s+([A-Z].*?)(?=\n\s*\[\d+\]|$)',
                r'\[(\d+)\]\s+([A-Z][a-zA-Z\s,]+\(\d{4}\)[^.]*\.(?:[^.]*\.)*)',
            ],
            
            # Dash-bracketed: "- [1] Author..."
            'dash_bracketed': [
                r'(?:^|\n)\s*-\s*\[(\d+)\]\s+([A-Z][^.]*\.(?:[^.]*\.)*)',
                r'(?:^|\n)\s*-\s*\[(\d+)\]\s+([A-Z].*?)(?=\n\s*-\s*\[\d+\]|$)',
            ],
            
            # Author-year format: "Smith, J. (2020). Title..."
            'author_year': [
                r'([A-Z][a-z]+,\s*[A-Z]\.(?:\s*[A-Z]\.)*)\s*\((\d{4})\)\.\s*([^.]+\.(?:[^.]*\.)*)',
                r'([A-Z][a-z]+\s+et\s+al\.)\s*\((\d{4})\)\.\s*([^.]+\.(?:[^.]*\.)*)',
                r'([A-Z][a-z]+,\s*[A-Z][a-z]+)\s*\((\d{4})\)\.\s*([^.]+\.(?:[^.]*\.)*)',
            ],
            
            # DOI patterns
            'doi': [
                r'(doi:\s*10\.\d+/[^\s]+)',
                r'(DOI:\s*10\.\d+/[^\s]+)',
                r'(https?://doi\.org/10\.\d+/[^\s]+)',
            ],
            
            # PubMed patterns
            'pubmed': [
                r'(PMID:\s*\d+)',
                r'(PubMed\s+ID:\s*\d+)',
            ]
        }
    
    def _build_section_headers(self) -> List[str]:
        """Build patterns for finding reference sections."""
        return [
            r'(?:^|\n)\s*#{1,6}\s*\*?\*?\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited)\s*\*?\*?\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:REFERENCES?|Bibliography|Citations?|Literature\s+Cited)\s*(?:\n|$)',
            r'(?:^|\n)\s*\d+\.\s*(?:REFERENCES?|Bibliography)\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:References?|Bibliography)\s*(?:\n|$)',
            r'(?:^|\n)\s*\*\*\s*(?:REFERENCES?|Bibliography|Citations?)\s*\*\*\s*(?:\n|$)',
            r'(?:^|\n)\s*(?:REFERENCES?|Bibliography|Citations?)\s*:?\s*(?:\n|$)',
        ]
    
    def extract_text_pymupdf(self, pdf_path: str) -> str:
        """Extract text using PyMuPDF (better quality)."""
        if not PYMUPDF_AVAILABLE:
            return ""
        
        try:
            doc = fitz.open(pdf_path)
            text = ""
            
            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                page_text = page.get_text()
                text += page_text + "\n\n"
            
            doc.close()
            logger.info(f"✅ PyMuPDF extracted {len(text):,} characters")
            return text
            
        except Exception as e:
            logger.error(f"❌ PyMuPDF extraction failed: {e}")
            return ""
    
    def extract_text_pypdf2(self, pdf_path: str) -> str:
        """Extract text using PyPDF2 (fallback)."""
        if not PYPDF2_AVAILABLE:
            return ""
        
        try:
            with open(pdf_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                text = ""
                
                for page in reader.pages:
                    page_text = page.extract_text()
                    text += page_text + "\n\n"
            
            logger.info(f"✅ PyPDF2 extracted {len(text):,} characters")
            return text
            
        except Exception as e:
            logger.error(f"❌ PyPDF2 extraction failed: {e}")
            return ""
    
    def find_reference_sections(self, text: str) -> List[str]:
        """Find reference sections in the document."""
        sections = []

        for header_pattern in self.section_headers:
            try:
                matches = list(re.finditer(header_pattern, text, re.IGNORECASE | re.MULTILINE))

                for match in matches:
                    start = match.end()

                    # Look for end of references section
                    end_patterns = [
                        r'(?:\n|^)\s*(?:Appendix|Acknowledgments?|Author\s+Information|Supplementary|Figure|Table)\s*(?:\n|$)',
                        r'(?:\n|^)\s*\d+\.\s*(?:Appendix|Acknowledgments?)\s*(?:\n|$)',
                        r'(?:\n|^)\s*#{1,6}\s*(?:Appendix|Acknowledgments?)\s*(?:\n|$)',
                    ]

                    end = len(text)
                    for end_pattern in end_patterns:
                        end_match = re.search(end_pattern, text[start:], re.IGNORECASE | re.MULTILINE)
                        if end_match:
                            end = start + end_match.start()
                            break

                    section = text[start:end].strip()

                    # Validate that this looks like a real reference section
                    if self._is_valid_reference_section(section):
                        sections.append(section)
                        logger.info(f"✅ Found valid reference section: {len(section):,} characters")
                    else:
                        logger.warning(f"⚠️ Skipped invalid reference section: {len(section):,} characters")

            except re.error as e:
                logger.warning(f"Regex error in header pattern: {e}")
                continue

        return sections

    def _is_valid_reference_section(self, section: str) -> bool:
        """Check if a section looks like a real reference section."""
        if len(section) < 200:  # Too short for a real reference section
            return False

        # Count potential references (lines that look like academic references)
        lines = section.split('\n')
        potential_refs = 0

        for line in lines:
            line = line.strip()
            if len(line) > 30:  # Substantial line
                # Check for academic reference indicators
                if any([
                    re.search(r'\b\d{4}\b', line),  # Year
                    re.search(r'\bet\s+al\.?', line, re.IGNORECASE),  # et al
                    re.search(r'[A-Z][a-z]+,\s*[A-Z]\.', line),  # Author format
                    re.search(r'\([12]\d{3}\)', line),  # Year in parentheses
                    re.search(r'\bJ\.|Journal|Proc\.', line, re.IGNORECASE),  # Journal
                ]):
                    potential_refs += 1

        # Should have at least 3 potential references to be considered valid
        if potential_refs < 3:
            logger.warning(f"Section has only {potential_refs} potential references")
            return False

        # Check reference density (should be mostly references, not other content)
        total_substantial_lines = len([l for l in lines if len(l.strip()) > 30])
        if total_substantial_lines > 0:
            ref_density = potential_refs / total_substantial_lines
            if ref_density < 0.3:  # Less than 30% of lines look like references
                logger.warning(f"Low reference density: {ref_density:.2f}")
                return False

        logger.info(f"Valid reference section: {potential_refs} potential references, density: {ref_density:.2f}")
        return True
    
    def extract_references_from_section(self, section: str) -> List[Dict[str, Any]]:
        """Extract references from a reference section using all patterns."""
        all_references = []
        
        for pattern_type, patterns in self.reference_patterns.items():
            for pattern in patterns:
                try:
                    matches = list(re.finditer(pattern, section, re.MULTILINE | re.DOTALL))
                    
                    for match in matches:
                        ref_data = self._parse_reference_match(match, pattern_type)
                        if ref_data and self._is_valid_reference(ref_data['text']):
                            all_references.append(ref_data)
                
                except re.error as e:
                    logger.warning(f"Regex error in pattern {pattern_type}: {e}")
                    continue
        
        # Deduplicate and sort
        unique_refs = self._deduplicate_references(all_references)
        logger.info(f"✅ Extracted {len(unique_refs)} unique references from section")
        
        return unique_refs
    
    def _parse_reference_match(self, match: re.Match, pattern_type: str) -> Optional[Dict[str, Any]]:
        """Parse a regex match into a structured reference."""
        try:
            groups = match.groups()
            
            if pattern_type in ['numbered_period', 'numbered_space', 'bracketed', 'dash_bracketed']:
                if len(groups) >= 2:
                    number = groups[0]
                    text = groups[1].strip()
                else:
                    number = None
                    text = match.group(0).strip()
            
            elif pattern_type == 'author_year':
                if len(groups) >= 3:
                    author = groups[0]
                    year = groups[1]
                    title = groups[2]
                    text = f"{author} ({year}). {title}".strip()
                    number = None
                else:
                    text = match.group(0).strip()
                    number = None
            
            else:  # DOI, PubMed, etc.
                text = match.group(0).strip()
                number = None
            
            return {
                'text': text,
                'number': number,
                'pattern_type': pattern_type,
                'uuid': str(uuid.uuid4())
            }
        
        except Exception as e:
            logger.warning(f"Error parsing reference match: {e}")
            return None
    
    def _is_valid_reference(self, text: str) -> bool:
        """Check if a text string is a valid academic reference."""
        if len(text) < 30:  # Too short for a real reference
            return False

        if len(text) > 2000:  # Too long
            return False

        # Must contain at least one period (for sentences)
        if '.' not in text:
            return False

        # Should contain some alphabetic characters
        if not re.search(r'[a-zA-Z]', text):
            return False

        # Should not be mostly numbers
        alpha_count = len(re.findall(r'[a-zA-Z]', text))
        if alpha_count < len(text) * 0.3:
            return False

        # Academic reference indicators (must have at least one)
        academic_indicators = [
            r'\b\d{4}\b',  # Year (4 digits)
            r'\bet\s+al\.?',  # "et al."
            r'\bvol\.?\s*\d+',  # Volume
            r'\bpp?\.?\s*\d+',  # Pages
            r'\bJ\.|Journal|Proc\.|Proceedings',  # Journal indicators
            r'\bDOI:|doi:',  # DOI
            r'\bPMID:',  # PubMed ID
            r'[A-Z][a-z]+,\s*[A-Z]\.(?:\s*[A-Z]\.)*',  # Author format: "Smith, J."
            r'\([12]\d{3}\)',  # Year in parentheses
        ]

        has_academic_indicator = any(re.search(pattern, text, re.IGNORECASE) for pattern in academic_indicators)
        if not has_academic_indicator:
            return False

        # Exclude common non-reference patterns
        exclusion_patterns = [
            r'^[A-Z][a-z\s]+\.$',  # Simple sentences like "Managing inflammation."
            r'^\d+\.\s*[A-Z][a-z\s]+\.$',  # Numbered simple sentences
            r'^(Ability|Influencing|Managing|Displacing|Localised)',  # Common document content starters
            r'^[A-Z][a-z\s]+(e\.g\.|i\.e\.)',  # Sentences with examples
            r'^\d+\.\s*[A-Z][a-z\s]+(tract|axis|response|effect|disorders)\.',  # Common content patterns
        ]

        for pattern in exclusion_patterns:
            if re.match(pattern, text, re.IGNORECASE):
                return False

        return True
    
    def _deduplicate_references(self, references: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate references."""
        seen_texts = set()
        unique_refs = []
        
        for ref in references:
            # Normalize text for comparison
            normalized = re.sub(r'\s+', ' ', ref['text'].lower().strip())
            
            if normalized not in seen_texts:
                seen_texts.add(normalized)
                unique_refs.append(ref)
        
        return unique_refs
    
    async def extract_references_from_document(self, file_path: str) -> Dict[str, Any]:
        """
        Main method to extract references from a document.
        
        Uses multiple text extraction methods and reference patterns.
        """
        logger.info(f"🔍 Starting robust reference extraction for: {Path(file_path).name}")
        
        all_references = []
        extraction_methods = []
        
        # Method 1: Try PyMuPDF first (better quality)
        if PYMUPDF_AVAILABLE:
            try:
                pymupdf_text = self.extract_text_pymupdf(file_path)
                if pymupdf_text:
                    sections = self.find_reference_sections(pymupdf_text)
                    if sections:
                        for section in sections:
                            refs = self.extract_references_from_section(section)
                            all_references.extend(refs)
                        extraction_methods.append("pymupdf_sections")
                    else:
                        # No sections found, try whole document
                        refs = self.extract_references_from_section(pymupdf_text)
                        all_references.extend(refs)
                        extraction_methods.append("pymupdf_whole")
            except Exception as e:
                logger.error(f"❌ PyMuPDF method failed: {e}")
        
        # Method 2: Try PyPDF2 if PyMuPDF didn't find enough references
        if len(all_references) < 10 and PYPDF2_AVAILABLE:
            try:
                pypdf2_text = self.extract_text_pypdf2(file_path)
                if pypdf2_text:
                    sections = self.find_reference_sections(pypdf2_text)
                    if sections:
                        for section in sections:
                            refs = self.extract_references_from_section(section)
                            all_references.extend(refs)
                        extraction_methods.append("pypdf2_sections")
                    else:
                        # No sections found, try whole document
                        refs = self.extract_references_from_section(pypdf2_text)
                        all_references.extend(refs)
                        extraction_methods.append("pypdf2_whole")
            except Exception as e:
                logger.error(f"❌ PyPDF2 method failed: {e}")
        
        # Final deduplication
        unique_references = self._deduplicate_references(all_references)
        
        logger.info(f"🎉 Robust extraction completed: {len(unique_references)} references found using {extraction_methods}")
        
        return {
            'success': True,
            'total_reference_count': len(unique_references),
            'references': unique_references,
            'extraction_methods': extraction_methods,
            'file_path': file_path
        }
