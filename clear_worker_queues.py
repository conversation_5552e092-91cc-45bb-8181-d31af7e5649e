#!/usr/bin/env python3
"""
Clear stuck worker queues and background tasks.
"""

import asyncio
import requests
from utils.logging_utils import get_logger

logger = get_logger(__name__)

async def clear_worker_queues():
    """Clear stuck worker queues and background tasks."""
    
    base_url = "http://localhost:9753"
    
    try:
        # Check queue sizes
        response = requests.get(f"{base_url}/api/queue-sizes")
        if response.status_code == 200:
            queue_data = response.json()
            logger.info(f"Current queue sizes: {queue_data}")
            
            # If there are items in queues, try to clear them
            total_queued = sum(queue_data.values()) if isinstance(queue_data, dict) else 0
            if total_queued > 0:
                logger.warning(f"Found {total_queued} items in worker queues")
                
                # Try to clear queues (if endpoint exists)
                clear_response = requests.post(f"{base_url}/api/clear-queues")
                if clear_response.status_code == 200:
                    logger.info("✅ Successfully cleared worker queues")
                else:
                    logger.warning(f"Could not clear queues: {clear_response.status_code}")
            else:
                logger.info("✅ No items in worker queues")
        else:
            logger.warning(f"Could not get queue sizes: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error checking worker queues: {e}")

    try:
        # Check worker status
        response = requests.get(f"{base_url}/api/worker-status")
        if response.status_code == 200:
            status_data = response.json()
            logger.info(f"Worker status: {status_data}")
        else:
            logger.warning(f"Could not get worker status: {response.status_code}")
            
    except Exception as e:
        logger.error(f"Error checking worker status: {e}")

if __name__ == "__main__":
    asyncio.run(clear_worker_queues())
