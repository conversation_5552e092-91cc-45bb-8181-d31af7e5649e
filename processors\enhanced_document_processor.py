"""
Enhanced Document Processing Pipeline
Supports multiple document types with unified processing workflow.
"""

import os
import asyncio
from typing import Dict, Any, Union, Optional, List
from pathlib import Path
import logging
from datetime import datetime

# Document type processors
from processors.pdf_processor import PDFProcessor
from services.document_processing.docx_processor import DocxProcessor as WordProcessor
from processors.docling_processor import DoclingProcessor
from processors.excel_processor import ExcelProcessor
from processors.powerpoint_processor import PowerPointProcessor
from processors.text_processor import TextProcessor
from processors.html_processor import HTMLProcessor
from processors.image_processor import ImageProcessor
from processors.onenote_processor import OneNoteProcessor

# Progress tracking
from utils.progress_tracker import ProgressTracker
from utils.file_utils import get_file_category, is_supported_file
from services.improved_reference_extractor import ImprovedReferenceExtractor
from services.presentation_reference_extractor import PresentationReferenceExtractor

logger = logging.getLogger(__name__)

class EnhancedDocumentProcessor:
    """
    Enhanced document processor supporting multiple file types with unified workflow.
    """

    def __init__(self):
        """Initialize the enhanced document processor."""
        self.processors = {
            'pdf': PDFProcessor(),
            'word': WordProcessor(),
            'docling': DoclingProcessor(),  # Advanced document processor for .doc and other formats
            'spreadsheet': ExcelProcessor(),
            'presentation': PowerPointProcessor(),
            'text': TextProcessor(),
            'html': HTMLProcessor(),
            'image': ImageProcessor(),
            'onenote': OneNoteProcessor(),
            'ebook': TextProcessor(),  # Use text processor for e-books (will need enhancement)
            'archive': TextProcessor(),  # Use text processor for archives (will need enhancement)
            'data': TextProcessor()  # Use text processor for data files (will need enhancement)
        }

        self.supported_extensions = {
            # Document formats
            '.pdf': 'docling',  # Use Docling for PDFs (better than pypdf)
            '.txt': 'text',
            '.md': 'docling',  # Use Docling for markdown
            '.markdown': 'text',
            '.rtf': 'text',
            '.doc': 'word',  # Use Word processor for .doc files (with fallbacks)
            '.docx': 'docling',  # Use Docling for .docx (better support)
            '.odt': 'word',
            '.pages': 'word',  # Apple Pages
            '.wpd': 'word',    # WordPerfect

            # Spreadsheet formats
            '.csv': 'docling',  # Use Docling for CSV
            '.xls': 'spreadsheet',
            '.xlsx': 'docling',  # Use Docling for .xlsx (better support)
            '.ods': 'spreadsheet',  # OpenDocument Spreadsheet
            '.numbers': 'spreadsheet',  # Apple Numbers

            # Presentation formats
            '.ppt': 'presentation',
            '.pptx': 'docling',  # Use Docling for .pptx (better support)
            '.odp': 'presentation',  # OpenDocument Presentation
            '.key': 'presentation',  # Apple Keynote

            # Note-taking formats
            '.one': 'onenote',  # Microsoft OneNote

            # Web formats
            '.html': 'docling',  # Use Docling for HTML (better support)
            '.htm': 'docling',   # Use Docling for HTM (better support)
            '.xml': 'html',
            '.xhtml': 'html',
            '.mhtml': 'html',

            # E-book formats
            '.epub': 'ebook',
            '.mobi': 'ebook',
            '.azw': 'ebook',
            '.azw3': 'ebook',

            # Image formats (with OCR support)
            '.jpg': 'docling',   # Use Docling for JPEG (better OCR)
            '.jpeg': 'docling',  # Use Docling for JPEG (better OCR)
            '.png': 'docling',   # Use Docling for PNG (better OCR)
            '.gif': 'image',
            '.tiff': 'docling',  # Use Docling for TIFF (better OCR)
            '.tif': 'docling',   # Use Docling for TIF (better OCR)
            '.bmp': 'docling',   # Use Docling for BMP (better OCR)
            '.webp': 'docling',  # Use Docling for WebP (better OCR)
            '.svg': 'image',

            # Archive formats (for batch processing)
            '.zip': 'archive',
            '.rar': 'archive',
            '.7z': 'archive',
            '.tar': 'archive',
            '.gz': 'archive',

            # Data formats
            '.json': 'data',
            '.yaml': 'data',
            '.yml': 'data',
            '.tsv': 'data',
        }

    async def process_document(
        self,
        file_path: Union[str, Path],
        chunk_size: int = 1200,
        overlap: int = 0,
        extract_entities: bool = True,
        extract_references: bool = True,
        extract_metadata: bool = True,
        generate_embeddings: bool = True,
        progress_callback: Optional[callable] = None,
        progress_tracker: Optional[ProgressTracker] = None
    ) -> Dict[str, Any]:
        """
        Process a document through the enhanced unified workflow.

        Args:
            file_path: Path to the document file
            chunk_size: Size of text chunks in characters
            overlap: Overlap between chunks in characters
            extract_entities: Whether to extract entities
            extract_references: Whether to extract references
            extract_metadata: Whether to extract metadata
            generate_embeddings: Whether to generate embeddings
            progress_callback: Optional callback for progress updates

        Returns:
            Processing result dictionary
        """
        file_path = Path(file_path)

        # Use provided progress tracker or create a new one
        if progress_tracker is None:
            progress_tracker = ProgressTracker(
                document_id=str(file_path.stem),
                filename=file_path.name,
                total_steps=7
            )

        try:
            # Step 1: Validate file
            progress_tracker.update_progress(
                step=1,
                step_name="Validating document",
                progress_percentage=10,
                status="processing"
            )

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            # Check for cancellation
            progress_tracker.check_cancellation()

            if not file_path.exists():
                raise FileNotFoundError(f"File not found: {file_path}")

            if not is_supported_file(file_path.name):
                raise ValueError(f"Unsupported file type: {file_path.suffix}")

            # Step 2: Determine processor and extract text
            progress_tracker.update_progress(
                step=2,
                step_name="Extracting text from document",
                progress_percentage=20,
                status="processing"
            )

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            # Check for cancellation
            progress_tracker.check_cancellation()

            file_category = self._get_file_category(file_path)
            processor = self.processors.get(file_category)

            if not processor:
                raise ValueError(f"No processor available for file category: {file_category}")

            # Extract text using appropriate processor
            # Handle different processor method names and parameter types
            if hasattr(processor, 'extract_text'):
                if file_category == 'pdf':
                    # PDF processor expects Path object
                    extraction_result = await processor.extract_text(Path(file_path))
                else:
                    # Other processors expect string
                    extraction_result = await processor.extract_text(file_path)
            elif hasattr(processor, 'process_document'):
                # DocxProcessor and some others use process_document method
                extraction_result = await processor.process_document(file_path)
            else:
                # Fallback - try extract_text anyway
                extraction_result = await processor.extract_text(file_path)

            if not extraction_result.get('success', False):
                raise Exception(f"Text extraction failed: {extraction_result.get('error', 'Unknown error')}")

            extracted_text = extraction_result.get('text', '')
            metadata = extraction_result.get('metadata', {})

            # Step 3: Chunk the text
            progress_tracker.update_progress(
                step=3,
                step_name="Chunking document text",
                progress_percentage=35,
                status="processing"
            )

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            # Check for cancellation
            progress_tracker.check_cancellation()

            chunks = await self._chunk_text(extracted_text, chunk_size, overlap)

            # Step 4: Store in knowledge graph
            progress_tracker.update_progress(
                step=4,
                step_name="Storing in knowledge graph",
                progress_percentage=50,
                status="processing"
            )

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            episode_id = await self._store_in_knowledge_graph(
                file_path, chunks, metadata
            )

            result = {
                'success': True,
                'episode_id': episode_id,
                'file_path': str(file_path),
                'file_type': file_category,
                'chunks': len(chunks),
                'text_length': len(extracted_text),
                'metadata': metadata,
                'ocr_provider': extraction_result.get('ocr_provider', 'unknown'),
                'processing_time': datetime.now().isoformat()
            }

            # Step 5: Extract entities (if requested)
            if extract_entities:
                progress_tracker.update_progress(
                    step=5,
                    step_name="Extracting entities",
                    progress_percentage=65,
                    status="processing"
                )

                if progress_callback:
                    await progress_callback(progress_tracker.get_progress_data())

                # Check for cancellation
                progress_tracker.check_cancellation()

                entities_result = await self._extract_entities(chunks, episode_id)
                result['entities'] = entities_result.get('count', 0)
                result['entity_types'] = entities_result.get('types', [])

                # Update progress tracker with entity count
                progress_tracker.update_entities_count(result['entities'])

            # Step 6: Extract references (if requested)
            if extract_references:
                progress_tracker.update_progress(
                    step=6,
                    step_name="Extracting references",
                    progress_percentage=80,
                    status="processing"
                )

                if progress_callback:
                    await progress_callback(progress_tracker.get_progress_data())

                # Check for cancellation
                progress_tracker.check_cancellation()

                references_result = await self._extract_references_improved(file_path)
                result['references'] = references_result.get('total_reference_count', 0)
                result['reference_file'] = references_result.get('csv_path')
                result['reference_extraction_method'] = references_result.get('extraction_method', 'unknown')

                # Update progress tracker with reference count
                progress_tracker.update_references_count(result['references'])

            # Step 7: Generate embeddings (if requested)
            if generate_embeddings:
                progress_tracker.update_progress(
                    step=7,
                    step_name="Generating embeddings",
                    progress_percentage=95,
                    status="processing"
                )

                if progress_callback:
                    await progress_callback(progress_tracker.get_progress_data())

                # Check for cancellation
                progress_tracker.check_cancellation()

                embeddings_result = await self._generate_embeddings(chunks, episode_id)
                result['embeddings'] = embeddings_result.get('count', 0)

                # Update progress tracker with embedding count
                progress_tracker.update_embeddings_count(result['embeddings'])

            # Update final counts in progress tracker
            progress_tracker.update_facts_count(len(chunks))  # Use chunks as facts count

            # Complete
            progress_tracker.complete(result)

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            logger.info(f"Successfully processed document: {file_path}")
            return result

        except RuntimeError as e:
            # Handle cancellation specifically
            if "cancelled" in str(e).lower():
                error_msg = f"Processing cancelled for document {file_path}"
                logger.info(error_msg)

                progress_tracker.fail(error_msg)

                if progress_callback:
                    await progress_callback(progress_tracker.get_progress_data())

                return {
                    'success': False,
                    'error': error_msg,
                    'cancelled': True,
                    'file_path': str(file_path),
                    'file_type': self._get_file_category(file_path) if file_path.exists() else 'unknown'
                }
            else:
                # Handle other runtime errors
                error_msg = f"Runtime error processing document {file_path}: {str(e)}"
                logger.error(error_msg)

                progress_tracker.fail(error_msg)

                if progress_callback:
                    await progress_callback(progress_tracker.get_progress_data())

                return {
                    'success': False,
                    'error': error_msg,
                    'file_path': str(file_path),
                    'file_type': self._get_file_category(file_path) if file_path.exists() else 'unknown'
                }
        except Exception as e:
            error_msg = f"Error processing document {file_path}: {str(e)}"
            logger.error(error_msg)

            progress_tracker.fail(error_msg)

            if progress_callback:
                await progress_callback(progress_tracker.get_progress_data())

            return {
                'success': False,
                'error': error_msg,
                'file_path': str(file_path),
                'file_type': self._get_file_category(file_path) if file_path.exists() else 'unknown'
            }

    def _get_file_category(self, file_path: Path) -> str:
        """Get the file category based on extension."""
        ext = file_path.suffix.lower()
        return self.supported_extensions.get(ext, 'other')

    async def _chunk_text(self, text: str, chunk_size: int, overlap: int) -> List[Dict[str, Any]]:
        """Chunk text into smaller pieces."""
        try:
            from utils.text_utils import split_text_recursively

            # Use existing chunking logic
            chunks = split_text_recursively(text, chunk_size, overlap)

            # Convert to expected format
            chunk_list = []
            for i, chunk in enumerate(chunks):
                chunk_dict = {
                    'id': i,
                    'text': chunk,
                    'start_index': text.find(chunk),
                    'end_index': text.find(chunk) + len(chunk),
                    'length': len(chunk)
                }
                chunk_list.append(chunk_dict)

            return chunk_list

        except Exception as e:
            logger.error(f"Error chunking text: {e}")
            # Fallback to simple chunking
            chunks = []
            for i in range(0, len(text), chunk_size):
                chunk_text = text[i:i + chunk_size]
                chunks.append({
                    'id': i // chunk_size,
                    'text': chunk_text,
                    'start_index': i,
                    'end_index': i + len(chunk_text),
                    'length': len(chunk_text)
                })
            return chunks

    async def _store_in_knowledge_graph(self, file_path: Path, chunks: List[Dict], metadata: Dict) -> str:
        """Store document and chunks in the knowledge graph."""
        try:
            # Import existing database functionality
            from database.database_service import create_episode_node, create_fact_node
            import uuid

            # Generate episode ID
            episode_id = str(uuid.uuid4())

            # Create Episode node
            episode_data = {
                'uuid': episode_id,
                'name': file_path.name,
                'file_path': str(file_path),
                'processed_at': datetime.now().isoformat(),
                'chunks_count': len(chunks),
                'metadata': metadata
            }

            # Store episode
            await create_episode_node(file_path.name, episode_data)

            # Store chunks as Facts and capture fact UUIDs
            fact_uuids = []
            for chunk in chunks:
                fact_data = {
                    'episode_id': episode_id,
                    'chunk_index': chunk['id'],
                    'start_index': chunk['start_index'],
                    'end_index': chunk['end_index']
                }
                fact_uuid = await create_fact_node(chunk['text'], episode_id, fact_data)
                if fact_uuid:
                    fact_uuids.append(fact_uuid)
                    # Store fact UUID in chunk for later use
                    chunk['fact_uuid'] = fact_uuid

            return episode_id

        except Exception as e:
            logger.error(f"Error storing in knowledge graph: {e}")
            raise

    async def _extract_entities(self, chunks: List[Dict], episode_id: str) -> Dict[str, Any]:
        """Extract entities from document chunks."""
        try:
            # Import existing entity extraction
            from services.entity_extraction_service import extract_entities_from_text

            total_entities = 0
            entity_types = set()

            for chunk in chunks:
                try:
                    # Get the fact UUID for this chunk
                    fact_id = chunk.get('fact_uuid')

                    if not fact_id:
                        logger.warning(f"No fact UUID found for chunk {chunk['id']}, skipping entity extraction")
                        continue

                    entities = await extract_entities_from_text(
                        chunk['text'],
                        document_id=episode_id,
                        fact_id=fact_id,
                        llm_provider='openrouter'
                    )
                    total_entities += len(entities)

                    for entity in entities:
                        entity_types.add(entity.get('type', 'Unknown'))

                except Exception as e:
                    logger.warning(f"Error extracting entities from chunk {chunk['id']}: {e}")
                    continue

            return {
                'count': total_entities,
                'types': list(entity_types)
            }

        except Exception as e:
            logger.error(f"Error in entity extraction: {e}")
            return {'count': 0, 'types': []}

    async def _extract_references(self, file_path: Path, text: str) -> Dict[str, Any]:
        """Extract references from document using advanced AI-powered extraction."""
        try:
            # Import improved reference processor
            from services.reference_processor import ReferenceProcessor

            processor = ReferenceProcessor()

            # Use advanced extraction method with the document text
            logger.info(f"🔍 Starting advanced reference extraction for {file_path.name}")
            advanced_result = await processor.extract_references_advanced(text, file_path.name)

            # Also run the traditional document-based extraction for CSV generation
            traditional_result = await processor.extract_references_from_document(str(file_path))

            # Combine results - use advanced count but traditional CSV path
            final_count = max(
                advanced_result.get('total_found', 0),
                traditional_result.get('total_reference_count', 0)
            )

            logger.info(f"📊 Reference extraction summary:")
            logger.info(f"   Advanced method: {advanced_result.get('total_found', 0)} references")
            logger.info(f"   Traditional method: {traditional_result.get('total_reference_count', 0)} references")
            logger.info(f"   Confidence score: {advanced_result.get('confidence_score', 0.0):.2f}")
            logger.info(f"   Extraction method: {advanced_result.get('extraction_method', 'unknown')}")

            return {
                'count': final_count,
                'file_path': traditional_result.get('csv_file_path', f"references/{file_path.stem}_references.csv"),
                'advanced_metadata': advanced_result.get('metadata', {}),
                'confidence_score': advanced_result.get('confidence_score', 0.0),
                'extraction_method': advanced_result.get('extraction_method', 'unknown')
            }

        except Exception as e:
            logger.error(f"❌ Error in advanced reference extraction: {e}")
            # Fallback to traditional method only
            try:
                logger.info("🔄 Falling back to traditional reference extraction")
                traditional_result = await processor.extract_references_from_document(str(file_path))
                return {
                    'count': traditional_result.get('total_reference_count', 0),
                    'file_path': traditional_result.get('csv_file_path', f"references/{file_path.stem}_references.csv"),
                    'extraction_method': 'fallback_traditional'
                }
            except Exception as fallback_error:
                logger.error(f"❌ Fallback reference extraction also failed: {fallback_error}")
                return {'count': 0, 'file_path': None, 'extraction_method': 'failed'}

    def _is_presentation_document(self, file_path: Path, text_sample: str = "") -> bool:
        """
        Detect if a document is likely a presentation (PowerPoint converted to PDF).

        Args:
            file_path: Path to the document file
            text_sample: Sample of extracted text for analysis

        Returns:
            True if document appears to be a presentation
        """
        # Check filename indicators
        filename_lower = file_path.name.lower()
        presentation_indicators = [
            'presentation', 'slides', 'powerpoint', 'ppt', 'keynote',
            'lecture', 'seminar', 'workshop', 'conference', 'talk'
        ]

        filename_score = sum(1 for indicator in presentation_indicators if indicator in filename_lower)

        # Check text content indicators (if available)
        content_score = 0
        if text_sample:
            content_indicators = [
                r'slide\s+\d+',  # "Slide 1", "Slide 2"
                r'##\s+',  # Markdown-style headers (common in converted presentations)
                r'^\s*\d+\.\s*$',  # Numbered bullet points on their own lines
                r'bullet\s+point',  # Explicit bullet point mentions
                r'next\s+slide',  # Navigation text
                r'thank\s+you\s+for\s+your\s+attention',  # Common presentation ending
                r'questions\?',  # Q&A sections
                r'overview|agenda|outline',  # Presentation structure words
            ]

            import re
            for pattern in content_indicators:
                if re.search(pattern, text_sample, re.IGNORECASE | re.MULTILINE):
                    content_score += 1

        # Decision logic
        if filename_score >= 1:  # Strong filename indicator
            return True
        elif content_score >= 2:  # Multiple content indicators
            return True
        elif filename_score >= 1 and content_score >= 1:  # Combination
            return True

        return False

    async def _extract_references_improved(self, file_path: Path) -> Dict[str, Any]:
        """
        Extract references using the improved Mistral OCR-based extractor.
        Automatically detects presentation documents and uses appropriate extractor.

        Args:
            file_path: Path to the document file

        Returns:
            Dictionary with extraction results
        """
        try:
            # Initialize Mistral OCR processor
            import os
            from utils.mistral_ocr import MistralOCRProcessor

            mistral_api_key = os.getenv('MISTRAL_API_KEY')
            if not mistral_api_key:
                logger.warning("❌ No Mistral API key found, falling back to traditional extraction")
                return await self._extract_references(file_path, "")

            mistral_ocr = MistralOCRProcessor(api_key=mistral_api_key)

            # Extract a sample of text to help with detection
            logger.info(f"🔍 Analyzing document type for {file_path.name}")

            # Handle OneNote files specially
            if file_path.suffix.lower() == '.one':
                logger.info(f"📝 Processing OneNote file for reference extraction: {file_path.name}")
                from processors.onenote_processor import OneNoteProcessor
                onenote_processor = OneNoteProcessor()
                result = await onenote_processor.extract_text(str(file_path))

                if result.get('success', False):
                    text_sample = result.get('text', '')[:2000]  # Use first 2000 chars
                    logger.info(f"✅ OneNote processor provided {len(text_sample)} characters for analysis")
                else:
                    logger.warning(f"⚠️ OneNote processor failed, falling back to traditional extraction")
                    return await self._extract_references(file_path, "")
            else:
                text_sample = await mistral_ocr.extract_text_from_pdf(str(file_path))

            # Detect if this is a presentation document
            is_presentation = self._is_presentation_document(file_path, text_sample[:2000])  # Use first 2000 chars

            if is_presentation:
                # Use presentation-specific extractor
                logger.info(f"🎯 Detected presentation document, using specialized extractor")
                presentation_extractor = PresentationReferenceExtractor(mistral_ocr)
                result = await presentation_extractor.extract_references(str(file_path))

                if result.get('success', False):
                    logger.info(f"✅ Presentation extraction found {result.get('total_reference_count', 0)} references")
                    return result
                else:
                    logger.warning(f"⚠️ Presentation extraction failed, trying improved method")

            # Use improved general extractor (fallback or for non-presentations)
            logger.info(f"🚀 Using improved reference extraction for {file_path.name}")
            improved_extractor = ImprovedReferenceExtractor(mistral_ocr)
            result = await improved_extractor.extract_references(str(file_path))

            if result.get('success', False):
                logger.info(f"✅ Improved extraction found {result.get('total_reference_count', 0)} references")
                return result
            else:
                logger.warning(f"⚠️ Improved extraction failed: {result.get('error', 'Unknown error')}")
                # Fallback to traditional method
                return await self._extract_references(file_path, "")

        except Exception as e:
            logger.error(f"❌ Error in improved reference extraction: {e}", exc_info=True)
            # Fallback to traditional method
            return await self._extract_references(file_path, "")

    async def _generate_embeddings(self, chunks: List[Dict], episode_id: str) -> Dict[str, Any]:
        """Generate embeddings for document chunks."""
        try:
            # Import existing embedding processor
            from services.embedding_processor import EmbeddingProcessor

            embedding_processor = EmbeddingProcessor()

            # Generate embeddings for the episode
            result = await embedding_processor.generate_embeddings_for_document(episode_id)

            return {
                'count': result.get('embeddings_generated', 0),
                'model': result.get('embedding_model', 'unknown')
            }

        except Exception as e:
            logger.error(f"Error in embedding generation: {e}")
            return {'count': 0}

    def get_supported_extensions(self) -> List[str]:
        """Get list of supported file extensions."""
        return list(self.supported_extensions.keys())

    def get_supported_categories(self) -> List[str]:
        """Get list of supported file categories."""
        return list(set(self.supported_extensions.values()))
